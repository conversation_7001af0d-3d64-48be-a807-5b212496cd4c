// MediaWiki 代码生成器

// 生成 MediaWiki 模板代码
function generateMediaWikiCode() {
    if (designData.elements.length === 0) {
        return '<!-- 暂无内容，请添加组件 -->';
    }
    
    let code = '<noinclude>\n{{documentation}}\n</noinclude><includeonly>\n';
    
    // 添加模板参数说明
    const templateParams = getTemplateParameters();
    if (templateParams.length > 0) {
        code += '<!-- 模板参数:\n';
        templateParams.forEach(param => {
            code += `   {{{${param.name}|${param.default}}}} - ${param.description || '参数描述'}\n`;
        });
        code += '-->\n\n';
    }
    
    // 生成主要内容
    code += generateElementsCode(designData.elements);
    
    code += '\n</includeonly>';
    
    return code;
}

// 生成元素代码
function generateElementsCode(elements) {
    let code = '';
    
    elements.forEach(element => {
        code += generateSingleElementCode(element);
    });
    
    return code;
}

// 生成单个元素的代码
function generateSingleElementCode(element) {
    const { type, properties, styles } = element;
    let code = '';
    
    switch (type) {
        case 'text':
            code = generateTextCode(properties, styles);
            break;
        case 'heading':
            code = generateHeadingCode(properties, styles);
            break;
        case 'image':
            code = generateImageCode(properties, styles);
            break;
        case 'link':
            code = generateLinkCode(properties, styles);
            break;
        case 'container':
            code = generateContainerCode(properties, styles);
            break;
        case 'row':
            code = generateRowCode(properties, styles);
            break;
        case 'column':
            code = generateColumnCode(properties, styles);
            break;
        case 'table':
            code = generateTableCode(properties, styles);
            break;
        case 'infobox':
            code = generateInfoboxCode(properties, styles);
            break;
        case 'navbox':
            code = generateNavboxCode(properties, styles);
            break;
        case 'template-param':
            code = generateTemplateParamCode(properties);
            break;
        default:
            code = `<!-- 未知组件类型: ${type} -->\n`;
    }
    
    return code;
}

// 生成文本代码
function generateTextCode(properties, styles) {
    const content = properties.content || '文本内容';
    const styleAttr = generateInlineStyles(styles);
    
    if (styleAttr) {
        return `<span style="${styleAttr}">${content}</span>\n`;
    }
    return `${content}\n`;
}

// 生成标题代码
function generateHeadingCode(properties, styles) {
    const content = properties.content || '标题';
    const level = properties.level || '2';
    const styleAttr = generateInlineStyles(styles);
    
    const equals = '='.repeat(parseInt(level) + 1);
    
    if (styleAttr) {
        return `${equals} <span style="${styleAttr}">${content}</span> ${equals}\n`;
    }
    return `${equals} ${content} ${equals}\n`;
}

// 生成图片代码
function generateImageCode(properties, styles) {
    const src = properties.src || 'Example.jpg';
    const alt = properties.alt || '图片描述';
    const width = properties.width;
    const height = properties.height;
    
    let imageOptions = [];
    if (width) imageOptions.push(`${width}px`);
    if (height) imageOptions.push(`x${height}px`);
    imageOptions.push('thumb');
    imageOptions.push(alt);
    
    return `[[File:${src}|${imageOptions.join('|')}]]\n`;
}

// 生成链接代码
function generateLinkCode(properties, styles) {
    const href = properties.href || '#';
    const text = properties.text || '链接文本';
    const styleAttr = generateInlineStyles(styles);
    
    if (href.startsWith('http')) {
        // 外部链接
        if (styleAttr) {
            return `[${href} <span style="${styleAttr}">${text}</span>]\n`;
        }
        return `[${href} ${text}]\n`;
    } else {
        // 内部链接
        if (styleAttr) {
            return `[[${href}|<span style="${styleAttr}">${text}</span>]]\n`;
        }
        return `[[${href}|${text}]]\n`;
    }
}

// 生成容器代码
function generateContainerCode(properties, styles) {
    const className = properties.class || 'container';
    const styleAttr = generateInlineStyles(styles);
    
    let code = `<div class="${className}"`;
    if (styleAttr) {
        code += ` style="${styleAttr}"`;
    }
    code += '>\n';
    code += '<!-- 容器内容 -->\n';
    code += '</div>\n';
    
    return code;
}

// 生成行代码
function generateRowCode(properties, styles) {
    const className = properties.class || 'row';
    const styleAttr = generateInlineStyles(styles);
    
    let code = `<div class="${className}"`;
    if (styleAttr) {
        code += ` style="${styleAttr}"`;
    }
    code += '>\n';
    code += '<!-- 行内容 -->\n';
    code += '</div>\n';
    
    return code;
}

// 生成列代码
function generateColumnCode(properties, styles) {
    const className = properties.class || 'column';
    const width = properties.width || '12';
    const styleAttr = generateInlineStyles(styles);
    
    let code = `<div class="${className} col-${width}"`;
    if (styleAttr) {
        code += ` style="${styleAttr}"`;
    }
    code += '>\n';
    code += '<!-- 列内容 -->\n';
    code += '</div>\n';
    
    return code;
}

// 生成表格代码
function generateTableCode(properties, styles) {
    const rows = parseInt(properties.rows) || 2;
    const cols = parseInt(properties.cols) || 2;
    const border = properties.border || '1';
    const styleAttr = generateInlineStyles(styles);
    
    let code = '{| class="wikitable"';
    if (styleAttr) {
        code += ` style="${styleAttr}"`;
    }
    code += '\n';
    
    // 表头
    code += '|-\n';
    for (let i = 1; i <= cols; i++) {
        code += `! 列${i}\n`;
    }
    
    // 数据行
    for (let i = 1; i < rows; i++) {
        code += '|-\n';
        for (let j = 1; j <= cols; j++) {
            code += `| 数据${i}-${j}\n`;
        }
    }
    
    code += '|}\n';
    
    return code;
}

// 生成信息框代码
function generateInfoboxCode(properties, styles) {
    const title = properties.title || '信息框标题';
    const className = properties.class || 'infobox';
    const styleAttr = generateInlineStyles(styles);
    
    let code = `{| class="${className}"`;
    if (styleAttr) {
        code += ` style="${styleAttr}"`;
    }
    code += '\n';
    code += `! colspan="2" | ${title}\n`;
    code += '|-\n';
    code += '| 标签1 || 值1\n';
    code += '|-\n';
    code += '| 标签2 || 值2\n';
    code += '|}\n';
    
    return code;
}

// 生成导航框代码
function generateNavboxCode(properties, styles) {
    const title = properties.title || '导航框';
    const className = properties.class || 'navbox';
    const styleAttr = generateInlineStyles(styles);
    
    let code = `{| class="${className}"`;
    if (styleAttr) {
        code += ` style="${styleAttr}"`;
    }
    code += '\n';
    code += `! ${title}\n`;
    code += '|-\n';
    code += '| [[链接1]] • [[链接2]] • [[链接3]]\n';
    code += '|}\n';
    
    return code;
}

// 生成模板参数代码
function generateTemplateParamCode(properties) {
    const name = properties.name || '参数名';
    const defaultValue = properties.default || '默认值';
    
    return `{{{${name}|${defaultValue}}}}\n`;
}

// 生成内联样式
function generateInlineStyles(styles) {
    const styleArray = [];
    
    for (const [property, value] of Object.entries(styles)) {
        if (value && value.trim() !== '') {
            // 转换CSS属性名为kebab-case
            const cssProperty = property.replace(/([A-Z])/g, '-$1').toLowerCase();
            styleArray.push(`${cssProperty}: ${value}`);
        }
    }
    
    return styleArray.join('; ');
}

// 生成CSS代码
function generateCSSCode() {
    if (designData.elements.length === 0) {
        return '/* 暂无样式 */';
    }
    
    let css = '/* MediaWiki 模板样式 */\n\n';
    
    // 为每个元素生成CSS类
    designData.elements.forEach((element, index) => {
        const className = generateCSSClassName(element.type, index);
        css += generateElementCSS(className, element.styles);
    });
    
    // 添加通用样式
    css += generateCommonCSS();
    
    return css;
}

// 生成CSS类名
function generateCSSClassName(type, index) {
    return `.template-${type}-${index + 1}`;
}

// 生成元素CSS
function generateElementCSS(className, styles) {
    if (Object.keys(styles).length === 0) {
        return '';
    }
    
    let css = `${className} {\n`;
    
    for (const [property, value] of Object.entries(styles)) {
        if (value && value.trim() !== '') {
            const cssProperty = property.replace(/([A-Z])/g, '-$1').toLowerCase();
            css += `    ${cssProperty}: ${value};\n`;
        }
    }
    
    css += '}\n\n';
    
    return css;
}

// 生成通用CSS
function generateCommonCSS() {
    return `/* 通用样式 */
.infobox {
    border: 1px solid #aaa;
    background: #f9f9f9;
    padding: 10px;
    margin: 10px 0;
    width: 300px;
    float: right;
    clear: right;
}

.navbox {
    border: 1px solid #aaa;
    background: #f0f0f0;
    padding: 5px;
    margin: 10px 0;
    width: 100%;
    text-align: center;
}

.template-param {
    background: #ffffcc;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}

.container {
    margin: 10px 0;
    padding: 10px;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 5px 0;
}

.column {
    flex: 1;
    padding: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .infobox {
        width: 100%;
        float: none;
    }
    
    .row {
        flex-direction: column;
    }
}
`;
}

// 获取模板参数
function getTemplateParameters() {
    const params = [];
    
    designData.elements.forEach(element => {
        if (element.type === 'template-param') {
            params.push({
                name: element.properties.name || '参数名',
                default: element.properties.default || '默认值',
                description: '模板参数'
            });
        }
    });
    
    return params;
}

// 生成预览HTML
function generatePreviewHTML() {
    if (designData.elements.length === 0) {
        return '<p>暂无内容预览</p>';
    }
    
    let html = '<div class="template-preview">';
    
    designData.elements.forEach((element, index) => {
        html += generateElementPreviewHTML(element, index);
    });
    
    html += '</div>';
    
    // 添加样式
    html += '<style>' + generateCSSCode() + '</style>';
    
    return html;
}

// 生成元素预览HTML
function generateElementPreviewHTML(element, index) {
    const { type, properties, styles } = element;
    const className = `template-${type}-${index + 1}`;
    const inlineStyles = generateInlineStyles(styles);
    
    let html = '';
    
    switch (type) {
        case 'text':
            html = `<p class="${className}" style="${inlineStyles}">${properties.content || '文本内容'}</p>`;
            break;
        case 'heading':
            const level = properties.level || '2';
            html = `<h${level} class="${className}" style="${inlineStyles}">${properties.content || '标题'}</h${level}>`;
            break;
        case 'image':
            if (properties.src) {
                html = `<img class="${className}" src="${properties.src}" alt="${properties.alt || ''}" style="${inlineStyles}">`;
            } else {
                html = `<div class="${className} image-placeholder" style="${inlineStyles}">图片占位符</div>`;
            }
            break;
        case 'link':
            html = `<a class="${className}" href="${properties.href || '#'}" style="${inlineStyles}">${properties.text || '链接'}</a>`;
            break;
        case 'container':
            html = `<div class="${className}" style="${inlineStyles}">容器内容</div>`;
            break;
        case 'infobox':
            html = `
                <table class="${className} infobox" style="${inlineStyles}">
                    <tr><th colspan="2">${properties.title || '信息框标题'}</th></tr>
                    <tr><td>标签1</td><td>值1</td></tr>
                    <tr><td>标签2</td><td>值2</td></tr>
                </table>
            `;
            break;
        case 'navbox':
            html = `
                <div class="${className} navbox" style="${inlineStyles}">
                    <div class="navbox-title">${properties.title || '导航框'}</div>
                    <div>链接1 • 链接2 • 链接3</div>
                </div>
            `;
            break;
        case 'template-param':
            html = `<span class="${className} template-param" style="${inlineStyles}">{{{${properties.name || '参数名'}|${properties.default || '默认值'}}}}</span>`;
            break;
        default:
            html = `<div class="${className}" style="${inlineStyles}">未知组件</div>`;
    }
    
    return html;
}
