/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
}

.app-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 工具栏样式 */
.toolbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.toolbar h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

.toolbar-right {
    display: flex;
    gap: 1rem;
}

/* 按钮样式 */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-primary {
    background-color: #4f46e5;
    color: white;
}

.btn-success {
    background-color: #10b981;
    color: white;
}

.btn-danger {
    background-color: #ef4444;
    color: white;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

/* 组件面板 */
.components-panel {
    width: 280px;
    background: white;
    border-right: 1px solid #e5e7eb;
    padding: 1.5rem;
    overflow-y: auto;
}

.components-panel h3 {
    color: #374151;
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.component-category {
    margin-bottom: 2rem;
}

.component-category h4 {
    color: #6b7280;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.component-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem;
    margin-bottom: 0.75rem;
    background: #f9fafb;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    cursor: grab;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    position: relative;
    overflow: hidden;
}

.component-item:hover {
    background: #ffffff;
    border-color: #4f46e5;
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(79, 70, 229, 0.15);
}

.component-item:active {
    cursor: grabbing;
    transform: translateY(0);
}

.component-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
}

.component-info {
    flex: 1;
    min-width: 0;
}

.component-name {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.25rem;
    font-size: 0.95rem;
}

.component-desc {
    color: #6b7280;
    font-size: 0.8rem;
    line-height: 1.4;
    word-wrap: break-word;
}

/* 设计区域 */
.design-area {
    flex: 1;
    background: #f9fafb;
    padding: 2rem;
    overflow: auto;
}

.design-canvas {
    background: white;
    min-height: 600px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    position: relative;
    padding: 2rem;
}

.canvas-placeholder {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #9ca3af;
}

.canvas-placeholder i {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

.canvas-placeholder p {
    font-size: 1.1rem;
}

/* 拖拽区域样式 */
.drop-zone {
    min-height: 100px;
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #9ca3af;
    transition: all 0.3s ease;
    position: relative;
    padding: 1rem;
}

.drop-zone.drag-over {
    border-color: #4f46e5;
    background-color: #eef2ff;
    color: #4f46e5;
    transform: scale(1.02);
}

.drop-zone:empty::before {
    content: "拖拽组件到这里";
    font-size: 0.9rem;
    color: #9ca3af;
    pointer-events: none;
}

.drop-zone.drag-over:empty::before {
    content: "释放以添加组件";
    color: #4f46e5;
}

/* 嵌套元素样式 */
.nested-element {
    margin: 0.5rem 0;
    border-left: 3px solid #e5e7eb;
    padding-left: 1rem;
}

.nested-element.selected {
    border-left-color: #4f46e5;
}

/* 容器占位符样式 */
.container-placeholder,
.row-placeholder,
.column-placeholder {
    padding: 2rem;
    text-align: center;
    color: #9ca3af;
    font-style: italic;
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    background-color: #f9fafb;
    transition: all 0.3s ease;
}

.container-placeholder:hover,
.row-placeholder:hover,
.column-placeholder:hover {
    border-color: #9ca3af;
    background-color: #f3f4f6;
}

/* 设计元素样式 */
.design-element {
    position: relative;
    margin: 0.5rem 0;
    padding: 0.5rem;
    border: 2px solid transparent;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: rgba(255, 255, 255, 0.5);
}

.design-element:hover {
    border-color: #d1d5db;
    background-color: rgba(249, 250, 251, 0.8);
}

.design-element.selected {
    border-color: #4f46e5;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
    background-color: rgba(238, 242, 255, 0.3);
}

.design-element[draggable="true"] {
    cursor: grab;
}

.design-element[draggable="true"]:active {
    cursor: grabbing;
}

.design-element .element-controls {
    position: absolute;
    top: -30px;
    right: 0;
    display: none;
    gap: 0.25rem;
}

.design-element:hover .element-controls,
.design-element.selected .element-controls {
    display: flex;
}

.element-control-btn {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.element-control-btn.delete {
    background-color: #ef4444;
    color: white;
}

.element-control-btn.duplicate {
    background-color: #10b981;
    color: white;
}

/* 新组件样式 */
.tabs-container {
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
}

.tab-headers {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #ddd;
}

.tab-header {
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-right: 1px solid #ddd;
    transition: background-color 0.3s ease;
}

.tab-header:last-child {
    border-right: none;
}

.tab-header:hover {
    background: #e9ecef;
}

.tab-header.active {
    background: white;
    border-bottom: 2px solid #4f46e5;
}

.collapsible {
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
}

.collapsible-header {
    padding: 1rem;
    background: #f8f9fa;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: background-color 0.3s ease;
}

.collapsible-header:hover {
    background: #e9ecef;
}

.collapsible-content {
    padding: 1rem;
    border-top: 1px solid #ddd;
}

.gallery {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 1rem;
}

.gallery-title {
    font-weight: bold;
    margin-bottom: 1rem;
    text-align: center;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.gallery-item {
    text-align: center;
}

.gallery-caption {
    margin-top: 0.5rem;
    font-size: 0.9rem;
    color: #666;
}

.notice {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
}

.notice-info {
    background: #e3f2fd;
    border-left: 4px solid #2196f3;
}

.notice-warning {
    background: #fff3e0;
    border-left: 4px solid #ff9800;
}

.notice-error {
    background: #ffebee;
    border-left: 4px solid #f44336;
}

.notice-success {
    background: #e8f5e8;
    border-left: 4px solid #4caf50;
}

.notice-icon {
    font-size: 1.2rem;
    margin-top: 0.2rem;
}

.notice-title {
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.category-tag {
    background: #f0f0f0;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 0.5rem;
    font-family: monospace;
    color: #666;
}

/* 属性面板 */
.properties-panel {
    width: 360px;
    background: white;
    border-left: 1px solid #e5e7eb;
    display: flex;
    flex-direction: column;
}

.panel-tabs {
    display: flex;
    border-bottom: 1px solid #e5e7eb;
}

.tab-btn {
    flex: 1;
    padding: 1rem;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 0.9rem;
    color: #6b7280;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.tab-btn:hover {
    background-color: #f9fafb;
}

.tab-btn.active {
    color: #4f46e5;
    border-bottom: 2px solid #4f46e5;
    background-color: #eef2ff;
}

.tab-content {
    flex: 1;
    overflow-y: auto;
}

.tab-pane {
    display: none;
    padding: 1.5rem;
}

.tab-pane.active {
    display: block;
}

.no-selection {
    text-align: center;
    color: #9ca3af;
    padding: 3rem 1rem;
}

.no-selection i {
    font-size: 2rem;
    margin-bottom: 1rem;
    display: block;
}

/* 属性编辑器样式 */
.property-header {
    padding: 1rem 0;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 1.5rem;
}

.property-header h4 {
    color: #374151;
    margin-bottom: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.property-header small {
    color: #6b7280;
    font-size: 0.8rem;
}

.property-row {
    margin-bottom: 1.5rem;
}

.property-label {
    display: block;
    font-size: 0.9rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

.property-label small {
    display: block;
    font-weight: normal;
    color: #6b7280;
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

.property-row input,
.property-row textarea,
.property-row select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    background-color: white;
}

.property-row input:focus,
.property-row textarea:focus,
.property-row select:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.property-row textarea {
    resize: vertical;
    min-height: 80px;
    font-family: inherit;
}

.property-row select {
    cursor: pointer;
}

/* 样式编辑器 */
.style-group {
    margin-bottom: 2rem;
}

.style-group h4 {
    color: #374151;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.style-row {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
}

.style-row label {
    flex: 1;
    font-size: 0.9rem;
    color: #6b7280;
    font-weight: 500;
}

.style-row input {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 0.9rem;
}

.style-row input:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
}

/* 代码区域 */
.code-section {
    margin-bottom: 2rem;
}

.code-section h4 {
    color: #374151;
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
}

.code-section textarea {
    width: 100%;
    height: 200px;
    padding: 1rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    resize: vertical;
    background-color: #f9fafb;
}

.code-section textarea:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(4px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 12px;
    width: 80%;
    max-width: 800px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
}

.modal-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.2rem;
}

.close {
    font-size: 1.5rem;
    font-weight: bold;
    cursor: pointer;
    color: white;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.close:hover {
    opacity: 1;
}

.modal-body {
    padding: 2rem;
    max-height: 60vh;
    overflow-y: auto;
}

.export-section {
    margin-bottom: 2rem;
}

.export-section h4 {
    color: #374151;
    margin-bottom: 1rem;
}

.export-section textarea {
    width: 100%;
    height: 150px;
    padding: 1rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    margin-bottom: 1rem;
    background-color: #f9fafb;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .components-panel {
        width: 240px;
    }
    
    .properties-panel {
        width: 280px;
    }
}

@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }
    
    .components-panel,
    .properties-panel {
        width: 100%;
        height: 200px;
    }
    
    .toolbar {
        padding: 1rem;
    }
    
    .toolbar h1 {
        font-size: 1.2rem;
    }
    
    .toolbar-right {
        gap: 0.5rem;
    }
    
    .btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.design-element {
    animation: fadeIn 0.3s ease;
}

/* 拖拽提示样式 */
.drag-hint {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    animation: fadeIn 0.3s ease;
}

.hint-content {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    max-width: 400px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    text-align: left;
}

.hint-content i {
    color: #4f46e5;
    font-size: 1.2rem;
    margin-right: 0.5rem;
}

.hint-content p {
    margin: 0.5rem 0;
    font-weight: 600;
    color: #374151;
}

.hint-content ul {
    margin: 1rem 0;
    padding-left: 1.5rem;
    color: #6b7280;
}

.hint-content li {
    margin: 0.5rem 0;
    font-size: 0.9rem;
}

.hint-content button {
    background: #4f46e5;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    margin-top: 1rem;
    transition: background-color 0.3s ease;
}

.hint-content button:hover {
    background: #4338ca;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}
