<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MediaWiki 模板可视化设计器</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- 顶部工具栏 -->
        <header class="toolbar">
            <div class="toolbar-left">
                <h1><i class="fas fa-code"></i> MediaWiki 模板设计器</h1>
            </div>
            <div class="toolbar-right">
                <button id="previewBtn" class="btn btn-primary">
                    <i class="fas fa-eye"></i> 预览
                </button>
                <button id="exportBtn" class="btn btn-success">
                    <i class="fas fa-download"></i> 导出代码
                </button>
                <button id="clearBtn" class="btn btn-danger">
                    <i class="fas fa-trash"></i> 清空
                </button>
                <button id="aiAssistantBtn" class="btn btn-info">
                    <i class="fas fa-robot"></i> AI 助手
                </button>
                <button id="aiConfigBtn" class="btn btn-secondary">
                    <i class="fas fa-cog"></i> AI 配置
                </button>
            </div>
        </header>

        <div class="main-content">
            <!-- 左侧组件面板 -->
            <aside class="components-panel">
                <h3><i class="fas fa-puzzle-piece"></i> 组件库</h3>
                
                <div class="component-category">
                    <h4>📝 基础组件</h4>
                    <div class="component-item" data-type="text" draggable="true">
                        <div class="component-icon">
                            <i class="fas fa-font"></i>
                        </div>
                        <div class="component-info">
                            <div class="component-name">文本段落</div>
                            <div class="component-desc">添加普通文本内容，支持段落格式</div>
                        </div>
                    </div>
                    <div class="component-item" data-type="heading" draggable="true">
                        <div class="component-icon">
                            <i class="fas fa-heading"></i>
                        </div>
                        <div class="component-info">
                            <div class="component-name">标题</div>
                            <div class="component-desc">创建H1-H6级别标题</div>
                        </div>
                    </div>
                    <div class="component-item" data-type="image" draggable="true">
                        <div class="component-icon">
                            <i class="fas fa-image"></i>
                        </div>
                        <div class="component-info">
                            <div class="component-name">图片</div>
                            <div class="component-desc">插入图片，支持缩略图和说明</div>
                        </div>
                    </div>
                    <div class="component-item" data-type="link" draggable="true">
                        <div class="component-icon">
                            <i class="fas fa-link"></i>
                        </div>
                        <div class="component-info">
                            <div class="component-name">链接</div>
                            <div class="component-desc">创建内部或外部链接</div>
                        </div>
                    </div>
                    <div class="component-item" data-type="list" draggable="true">
                        <div class="component-icon">
                            <i class="fas fa-list-ul"></i>
                        </div>
                        <div class="component-info">
                            <div class="component-name">列表</div>
                            <div class="component-desc">创建有序或无序列表</div>
                        </div>
                    </div>
                    <div class="component-item" data-type="quote" draggable="true">
                        <div class="component-icon">
                            <i class="fas fa-quote-left"></i>
                        </div>
                        <div class="component-info">
                            <div class="component-name">引用</div>
                            <div class="component-desc">添加引用文本或代码块</div>
                        </div>
                    </div>
                </div>

                <div class="component-category">
                    <h4>🏗️ 布局组件</h4>
                    <div class="component-item" data-type="container" draggable="true">
                        <div class="component-icon">
                            <i class="fas fa-square"></i>
                        </div>
                        <div class="component-info">
                            <div class="component-name">容器</div>
                            <div class="component-desc">通用容器，可包含其他元素</div>
                        </div>
                    </div>
                    <div class="component-item" data-type="row" draggable="true">
                        <div class="component-icon">
                            <i class="fas fa-grip-lines"></i>
                        </div>
                        <div class="component-info">
                            <div class="component-name">行布局</div>
                            <div class="component-desc">水平排列元素的行容器</div>
                        </div>
                    </div>
                    <div class="component-item" data-type="column" draggable="true">
                        <div class="component-icon">
                            <i class="fas fa-columns"></i>
                        </div>
                        <div class="component-info">
                            <div class="component-name">列布局</div>
                            <div class="component-desc">垂直排列元素的列容器</div>
                        </div>
                    </div>
                    <div class="component-item" data-type="table" draggable="true">
                        <div class="component-icon">
                            <i class="fas fa-table"></i>
                        </div>
                        <div class="component-info">
                            <div class="component-name">表格</div>
                            <div class="component-desc">创建数据表格，支持标题行</div>
                        </div>
                    </div>
                    <div class="component-item" data-type="tabs" draggable="true">
                        <div class="component-icon">
                            <i class="fas fa-folder-open"></i>
                        </div>
                        <div class="component-info">
                            <div class="component-name">标签页</div>
                            <div class="component-desc">创建多标签页内容区域</div>
                        </div>
                    </div>
                    <div class="component-item" data-type="collapsible" draggable="true">
                        <div class="component-icon">
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="component-info">
                            <div class="component-name">折叠框</div>
                            <div class="component-desc">可展开/折叠的内容区域</div>
                        </div>
                    </div>
                </div>

                <div class="component-category">
                    <h4>🎯 MediaWiki 特殊</h4>
                    <div class="component-item" data-type="infobox" draggable="true">
                        <div class="component-icon">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div class="component-info">
                            <div class="component-name">信息框</div>
                            <div class="component-desc">标准信息框，常用于人物、地点介绍</div>
                        </div>
                    </div>
                    <div class="component-item" data-type="navbox" draggable="true">
                        <div class="component-icon">
                            <i class="fas fa-bars"></i>
                        </div>
                        <div class="component-info">
                            <div class="component-name">导航框</div>
                            <div class="component-desc">页面底部导航模板</div>
                        </div>
                    </div>
                    <div class="component-item" data-type="template-param" draggable="true">
                        <div class="component-icon">
                            <i class="fas fa-code-branch"></i>
                        </div>
                        <div class="component-info">
                            <div class="component-name">模板参数</div>
                            <div class="component-desc">插入可配置的模板参数</div>
                        </div>
                    </div>
                    <div class="component-item" data-type="category" draggable="true">
                        <div class="component-icon">
                            <i class="fas fa-tags"></i>
                        </div>
                        <div class="component-info">
                            <div class="component-name">分类标签</div>
                            <div class="component-desc">为页面添加分类</div>
                        </div>
                    </div>
                    <div class="component-item" data-type="gallery" draggable="true">
                        <div class="component-icon">
                            <i class="fas fa-images"></i>
                        </div>
                        <div class="component-info">
                            <div class="component-name">图片画廊</div>
                            <div class="component-desc">创建图片展示画廊</div>
                        </div>
                    </div>
                    <div class="component-item" data-type="notice" draggable="true">
                        <div class="component-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="component-info">
                            <div class="component-name">提示框</div>
                            <div class="component-desc">警告、注意、提示等消息框</div>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- 中间设计区域 -->
            <main class="design-area">
                <div class="design-canvas" id="designCanvas">
                    <div class="canvas-placeholder">
                        <i class="fas fa-mouse-pointer"></i>
                        <p>拖拽组件到这里开始设计</p>
                    </div>
                    <div class="resize-grid"></div>
                </div>
            </main>

            <!-- 右侧属性面板 -->
            <aside class="properties-panel">
                <div class="panel-tabs">
                    <button class="tab-btn active" data-tab="properties">
                        <i class="fas fa-cog"></i> 属性
                    </button>
                    <button class="tab-btn" data-tab="styles">
                        <i class="fas fa-palette"></i> 样式
                    </button>
                    <button class="tab-btn" data-tab="code">
                        <i class="fas fa-code"></i> 代码
                    </button>
                    <button class="tab-btn" data-tab="examples">
                        <i class="fas fa-book"></i> 示例
                    </button>
                </div>

                <div class="tab-content">
                    <!-- 属性面板 -->
                    <div id="properties-tab" class="tab-pane active">
                        <div id="no-selection" class="no-selection">
                            <i class="fas fa-hand-pointer"></i>
                            <p>选择一个元素来编辑属性</p>
                        </div>
                        <div id="element-properties" class="element-properties" style="display: none;">
                            <!-- 动态生成的属性表单 -->
                        </div>
                    </div>

                    <!-- 样式面板 -->
                    <div id="styles-tab" class="tab-pane">
                        <div class="style-group">
                            <h4>布局</h4>
                            <div class="style-row">
                                <label>宽度:</label>
                                <input type="text" id="style-width" placeholder="auto">
                            </div>
                            <div class="style-row">
                                <label>高度:</label>
                                <input type="text" id="style-height" placeholder="auto">
                            </div>
                            <div class="style-row">
                                <label>边距:</label>
                                <input type="text" id="style-margin" placeholder="0">
                            </div>
                            <div class="style-row">
                                <label>内边距:</label>
                                <input type="text" id="style-padding" placeholder="0">
                            </div>
                        </div>

                        <div class="style-group">
                            <h4>文本</h4>
                            <div class="style-row">
                                <label>字体大小:</label>
                                <input type="text" id="style-font-size" placeholder="14px">
                            </div>
                            <div class="style-row">
                                <label>字体颜色:</label>
                                <input type="color" id="style-color" value="#000000">
                            </div>
                            <div class="style-row">
                                <label>背景色:</label>
                                <input type="color" id="style-background" value="#ffffff">
                            </div>
                        </div>

                        <div class="style-group">
                            <h4>边框</h4>
                            <div class="style-row">
                                <label>边框:</label>
                                <input type="text" id="style-border" placeholder="1px solid #ccc">
                            </div>
                            <div class="style-row">
                                <label>圆角:</label>
                                <input type="text" id="style-border-radius" placeholder="0">
                            </div>
                        </div>
                    </div>

                    <!-- 代码面板 -->
                    <div id="code-tab" class="tab-pane">
                        <div class="code-section">
                            <h4>MediaWiki 模板代码</h4>
                            <textarea id="mediawiki-code" readonly></textarea>
                            <button class="btn btn-sm btn-primary" onclick="copyCode('mediawiki-code')">
                                <i class="fas fa-copy"></i> 复制
                            </button>
                        </div>
                        <div class="code-section">
                            <h4>CSS 样式代码</h4>
                            <textarea id="css-code" readonly></textarea>
                            <button class="btn btn-sm btn-primary" onclick="copyCode('css-code')">
                                <i class="fas fa-copy"></i> 复制
                            </button>
                        </div>
                    </div>

                    <!-- 示例面板 -->
                    <div id="examples-tab" class="tab-pane">
                        <div class="examples-section">
                            <h4>常用模板示例</h4>
                            <div class="example-item" onclick="loadExample('infobox-person')">
                                <div class="example-icon"><i class="fas fa-user"></i></div>
                                <div class="example-info">
                                    <div class="example-name">人物信息框</div>
                                    <div class="example-desc">适用于人物介绍的标准信息框</div>
                                </div>
                            </div>
                            <div class="example-item" onclick="loadExample('navigation-box')">
                                <div class="example-icon"><i class="fas fa-compass"></i></div>
                                <div class="example-info">
                                    <div class="example-name">导航模板</div>
                                    <div class="example-desc">页面底部的导航链接模板</div>
                                </div>
                            </div>
                            <div class="example-item" onclick="loadExample('notice-template')">
                                <div class="example-icon"><i class="fas fa-exclamation-circle"></i></div>
                                <div class="example-info">
                                    <div class="example-name">提示模板</div>
                                    <div class="example-desc">警告、注意事项等提示框</div>
                                </div>
                            </div>
                        </div>

                        <div class="tutorial-section">
                            <h4>快速教程</h4>
                            <div class="tutorial-item">
                                <h5><i class="fas fa-play-circle"></i> 创建你的第一个模板</h5>
                                <ol>
                                    <li>从左侧拖拽一个"文本段落"组件到设计区域</li>
                                    <li>点击选中该元素，在右侧属性面板修改内容</li>
                                    <li>切换到"代码"标签查看生成的MediaWiki代码</li>
                                    <li>点击"复制"按钮将代码复制到MediaWiki页面</li>
                                </ol>
                            </div>
                            <div class="tutorial-item">
                                <h5><i class="fas fa-lightbulb"></i> MediaWiki 语法提示</h5>
                                <ul>
                                    <li><code>{{{参数名|默认值}}}</code> - 模板参数语法</li>
                                    <li><code>[[文件:图片名.jpg|thumb|说明]]</code> - 图片语法</li>
                                    <li><code>[[页面名称|显示文本]]</code> - 内部链接语法</li>
                                    <li><code>[http://example.com 外部链接]</code> - 外部链接语法</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </aside>
        </div>
    </div>

    <!-- 预览模态框 -->
    <div id="previewModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>模板预览</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div id="preview-content"></div>
            </div>
        </div>
    </div>

    <!-- 导出模态框 -->
    <div id="exportModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>导出代码</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="export-section">
                    <h4>MediaWiki 模板代码</h4>
                    <textarea id="export-mediawiki" readonly></textarea>
                    <button class="btn btn-primary" onclick="copyCode('export-mediawiki')">
                        <i class="fas fa-copy"></i> 复制模板代码
                    </button>
                </div>
                <div class="export-section">
                    <h4>CSS 样式代码</h4>
                    <textarea id="export-css" readonly></textarea>
                    <button class="btn btn-primary" onclick="copyCode('export-css')">
                        <i class="fas fa-copy"></i> 复制CSS代码
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- AI 助手模态框 -->
    <div id="aiAssistantModal" class="modal">
        <div class="modal-content ai-modal">
            <div class="modal-header">
                <h3><i class="fas fa-robot"></i> AI 模板助手</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="ai-chat-container">
                    <div class="ai-messages" id="aiMessages">
                        <div class="ai-message ai-message-assistant">
                            <div class="ai-avatar"><i class="fas fa-robot"></i></div>
                            <div class="ai-content">
                                <p>你好！我是 MediaWiki 模板设计助手。你可以用自然语言描述你想要创建的模板，我会帮你自动生成。</p>
                                <p><strong>示例：</strong></p>
                                <ul>
                                    <li>"创建一个人物信息框，包含姓名、出生日期和职业"</li>
                                    <li>"制作一个导航模板，有5个链接"</li>
                                    <li>"做一个警告提示框"</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="ai-input-container">
                        <div class="ai-input-section">
                            <textarea id="aiInput" placeholder="描述你想要创建的模板..." rows="3"></textarea>
                            <div class="ai-input-actions">
                                <input type="file" id="aiImageUpload" accept="image/*" style="display: none;">
                                <button id="aiImageBtn" class="btn btn-secondary btn-sm">
                                    <i class="fas fa-image"></i> 上传图片
                                </button>
                                <button id="aiSendBtn" class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i> 发送
                                </button>
                            </div>
                        </div>
                        <div id="aiImagePreview" class="ai-image-preview" style="display: none;">
                            <img id="aiPreviewImg" src="" alt="上传的图片">
                            <button id="aiRemoveImage" class="btn btn-sm btn-danger">
                                <i class="fas fa-times"></i> 移除图片
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- AI 配置模态框 -->
    <div id="aiConfigModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-cog"></i> AI 助手配置</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="config-section">
                    <h4>API 配置</h4>
                    <div class="config-row">
                        <label for="aiProvider">AI 服务提供商:</label>
                        <select id="aiProvider">
                            <option value="openai">OpenAI</option>
                            <option value="claude">Anthropic Claude</option>
                            <option value="custom">自定义 API</option>
                        </select>
                    </div>
                    <div class="config-row">
                        <label for="aiApiEndpoint">API 端点:</label>
                        <input type="url" id="aiApiEndpoint" placeholder="https://api.openai.com/v1/chat/completions">
                    </div>
                    <div class="config-row">
                        <label for="aiApiKey">API 密钥:</label>
                        <input type="password" id="aiApiKey" placeholder="输入您的 API 密钥">
                    </div>
                    <div class="config-row">
                        <label for="aiModel">模型:</label>
                        <input type="text" id="aiModel" placeholder="请输入模型名称（如 gpt-4o, claude-3-5-sonnet 等）">
                    </div>
                    <div class="config-help">
                        <small>
                            <strong>常用模型参考：</strong><br>
                            • OpenAI: gpt-4o, gpt-4o-mini, gpt-4-turbo<br>
                            • Claude: claude-3-5-sonnet-20241022, claude-3-5-haiku-20241022<br>
                            • 请查看您的 AI 服务提供商文档获取最新可用模型
                        </small>
                    </div>
                    <div class="config-row">
                        <label for="aiTemperature">创造性 (0-1):</label>
                        <input type="range" id="aiTemperature" min="0" max="1" step="0.1" value="0.7">
                        <span id="temperatureValue">0.7</span>
                    </div>
                </div>
                <div class="config-section">
                    <h4>图片分析配置</h4>
                    <div class="config-row">
                        <label for="aiVisionEnabled">启用图片分析:</label>
                        <input type="checkbox" id="aiVisionEnabled" checked>
                    </div>
                    <div class="config-row">
                        <label for="aiImageMaxSize">最大图片大小 (MB):</label>
                        <input type="number" id="aiImageMaxSize" value="5" min="1" max="20">
                    </div>
                </div>
                <div class="config-actions">
                    <button id="aiConfigSave" class="btn btn-primary">
                        <i class="fas fa-save"></i> 保存配置
                    </button>
                    <button id="aiConfigTest" class="btn btn-secondary">
                        <i class="fas fa-vial"></i> 测试连接
                    </button>
                    <button id="aiConfigReset" class="btn btn-danger">
                        <i class="fas fa-undo"></i> 重置默认
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script src="mediawiki-generator.js"></script>
</body>
</html>
