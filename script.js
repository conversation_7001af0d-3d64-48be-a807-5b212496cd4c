// 全局变量
let selectedElement = null;
let elementCounter = 0;
let designData = {
    elements: [],
    styles: {}
};

// DOM 元素
const designCanvas = document.getElementById('designCanvas');
const componentsPanel = document.querySelector('.components-panel');
const propertiesPanel = document.querySelector('.properties-panel');

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    initializeDragAndDrop();
    initializeTabs();
    initializeEventListeners();
    initializeModals();
});

// 初始化拖拽功能
function initializeDragAndDrop() {
    // 组件拖拽开始
    const componentItems = document.querySelectorAll('.component-item');
    componentItems.forEach(item => {
        item.addEventListener('dragstart', function(e) {
            e.dataTransfer.setData('text/plain', this.dataset.type);
            this.style.opacity = '0.5';
        });

        item.addEventListener('dragend', function(e) {
            this.style.opacity = '1';
        });
    });

    // 设计画布拖拽处理
    designCanvas.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('drag-over');
    });

    designCanvas.addEventListener('dragleave', function(e) {
        this.classList.remove('drag-over');
    });

    designCanvas.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('drag-over');
        
        const componentType = e.dataTransfer.getData('text/plain');
        const rect = this.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        createElement(componentType, x, y);
    });
}

// 创建设计元素
function createElement(type, x = 0, y = 0) {
    elementCounter++;
    const elementId = `element_${elementCounter}`;
    
    // 移除占位符
    const placeholder = designCanvas.querySelector('.canvas-placeholder');
    if (placeholder) {
        placeholder.style.display = 'none';
    }

    const element = document.createElement('div');
    element.className = 'design-element';
    element.id = elementId;
    element.dataset.type = type;
    
    // 添加控制按钮
    const controls = document.createElement('div');
    controls.className = 'element-controls';
    controls.innerHTML = `
        <button class="element-control-btn duplicate" onclick="duplicateElement('${elementId}')">
            <i class="fas fa-copy"></i>
        </button>
        <button class="element-control-btn delete" onclick="deleteElement('${elementId}')">
            <i class="fas fa-trash"></i>
        </button>
    `;
    element.appendChild(controls);

    // 根据类型创建内容
    const content = createElementContent(type, elementId);
    element.appendChild(content);

    // 添加点击事件
    element.addEventListener('click', function(e) {
        e.stopPropagation();
        selectElement(this);
    });

    designCanvas.appendChild(element);
    
    // 保存到设计数据
    const elementData = {
        id: elementId,
        type: type,
        properties: getDefaultProperties(type),
        styles: getDefaultStyles(type)
    };
    
    designData.elements.push(elementData);
    
    // 自动选择新创建的元素
    selectElement(element);
    
    // 更新代码
    updateCode();
}

// 创建元素内容
function createElementContent(type, elementId) {
    const content = document.createElement('div');
    content.className = 'element-content';
    
    switch(type) {
        case 'text':
            content.innerHTML = '<p>这是一段文本内容</p>';
            break;
        case 'heading':
            content.innerHTML = '<h2>这是标题</h2>';
            break;
        case 'image':
            content.innerHTML = '<div class="image-placeholder"><i class="fas fa-image"></i><br>图片占位符</div>';
            break;
        case 'link':
            content.innerHTML = '<a href="#">这是一个链接</a>';
            break;
        case 'container':
            content.innerHTML = '<div class="container-placeholder">容器 - 拖拽其他元素到这里</div>';
            content.classList.add('drop-zone');
            break;
        case 'row':
            content.innerHTML = '<div class="row-placeholder">行布局</div>';
            break;
        case 'column':
            content.innerHTML = '<div class="column-placeholder">列布局</div>';
            break;
        case 'table':
            content.innerHTML = `
                <table border="1">
                    <tr><th>列1</th><th>列2</th></tr>
                    <tr><td>数据1</td><td>数据2</td></tr>
                </table>
            `;
            break;
        case 'infobox':
            content.innerHTML = `
                <div class="infobox">
                    <div class="infobox-title">信息框标题</div>
                    <div class="infobox-content">
                        <div class="infobox-row">
                            <span class="label">标签:</span>
                            <span class="value">值</span>
                        </div>
                    </div>
                </div>
            `;
            break;
        case 'navbox':
            content.innerHTML = `
                <div class="navbox">
                    <div class="navbox-title">导航框</div>
                    <div class="navbox-content">
                        <a href="#">链接1</a> | <a href="#">链接2</a> | <a href="#">链接3</a>
                    </div>
                </div>
            `;
            break;
        case 'template-param':
            content.innerHTML = '<span class="template-param">{{{参数名|默认值}}}</span>';
            break;
        default:
            content.innerHTML = '<div>未知组件类型</div>';
    }
    
    return content;
}

// 获取默认属性
function getDefaultProperties(type) {
    const defaults = {
        text: { content: '这是一段文本内容', tag: 'p' },
        heading: { content: '这是标题', level: '2' },
        image: { src: '', alt: '图片描述', width: '', height: '' },
        link: { href: '#', text: '这是一个链接', target: '_self' },
        container: { class: 'container' },
        row: { class: 'row' },
        column: { class: 'column', width: '12' },
        table: { rows: '2', cols: '2', border: '1' },
        infobox: { title: '信息框标题', class: 'infobox' },
        navbox: { title: '导航框', class: 'navbox' },
        'template-param': { name: '参数名', default: '默认值' }
    };
    
    return defaults[type] || {};
}

// 获取默认样式
function getDefaultStyles(type) {
    const defaults = {
        text: { 'font-size': '14px', 'color': '#000000' },
        heading: { 'font-size': '24px', 'font-weight': 'bold', 'color': '#000000' },
        image: { 'max-width': '100%', 'height': 'auto' },
        link: { 'color': '#0066cc', 'text-decoration': 'underline' },
        container: { 'padding': '10px', 'border': '1px solid #ccc' },
        row: { 'display': 'flex', 'flex-wrap': 'wrap' },
        column: { 'flex': '1', 'padding': '5px' },
        table: { 'border-collapse': 'collapse', 'width': '100%' },
        infobox: { 'border': '1px solid #aaa', 'background': '#f9f9f9', 'padding': '10px', 'width': '300px' },
        navbox: { 'border': '1px solid #aaa', 'background': '#f0f0f0', 'padding': '5px' },
        'template-param': { 'background': '#ffffcc', 'padding': '2px 4px', 'border-radius': '3px' }
    };
    
    return defaults[type] || {};
}

// 选择元素
function selectElement(element) {
    // 移除之前的选择
    const previousSelected = document.querySelector('.design-element.selected');
    if (previousSelected) {
        previousSelected.classList.remove('selected');
    }
    
    // 选择新元素
    element.classList.add('selected');
    selectedElement = element;
    
    // 更新属性面板
    updatePropertiesPanel();
    updateStylesPanel();
}

// 更新属性面板
function updatePropertiesPanel() {
    const noSelection = document.getElementById('no-selection');
    const elementProperties = document.getElementById('element-properties');
    
    if (!selectedElement) {
        noSelection.style.display = 'block';
        elementProperties.style.display = 'none';
        return;
    }
    
    noSelection.style.display = 'none';
    elementProperties.style.display = 'block';
    
    const elementData = designData.elements.find(el => el.id === selectedElement.id);
    if (!elementData) return;
    
    // 生成属性表单
    let propertiesHTML = `<h4>元素属性 (${elementData.type})</h4>`;
    
    for (const [key, value] of Object.entries(elementData.properties)) {
        propertiesHTML += `
            <div class="property-row">
                <label>${key}:</label>
                <input type="text" 
                       data-property="${key}" 
                       value="${value}" 
                       onchange="updateElementProperty('${selectedElement.id}', '${key}', this.value)">
            </div>
        `;
    }
    
    elementProperties.innerHTML = propertiesHTML;
}

// 更新样式面板
function updateStylesPanel() {
    if (!selectedElement) return;
    
    const elementData = designData.elements.find(el => el.id === selectedElement.id);
    if (!elementData) return;
    
    // 填充样式输入框
    document.getElementById('style-width').value = elementData.styles.width || '';
    document.getElementById('style-height').value = elementData.styles.height || '';
    document.getElementById('style-margin').value = elementData.styles.margin || '';
    document.getElementById('style-padding').value = elementData.styles.padding || '';
    document.getElementById('style-font-size').value = elementData.styles['font-size'] || '';
    document.getElementById('style-color').value = elementData.styles.color || '#000000';
    document.getElementById('style-background').value = elementData.styles.background || '#ffffff';
    document.getElementById('style-border').value = elementData.styles.border || '';
    document.getElementById('style-border-radius').value = elementData.styles['border-radius'] || '';
}

// 更新元素属性
function updateElementProperty(elementId, property, value) {
    const elementData = designData.elements.find(el => el.id === elementId);
    if (elementData) {
        elementData.properties[property] = value;
        
        // 更新DOM元素
        updateElementDOM(elementId);
        updateCode();
    }
}

// 更新元素样式
function updateElementStyle(elementId, property, value) {
    const elementData = designData.elements.find(el => el.id === elementId);
    if (elementData) {
        elementData.styles[property] = value;
        
        // 应用样式到DOM元素
        const element = document.getElementById(elementId);
        if (element) {
            element.style[property] = value;
        }
        
        updateCode();
    }
}

// 更新DOM元素内容
function updateElementDOM(elementId) {
    const element = document.getElementById(elementId);
    const elementData = designData.elements.find(el => el.id === elementId);
    
    if (!element || !elementData) return;
    
    const content = element.querySelector('.element-content');
    if (!content) return;
    
    // 根据类型和属性更新内容
    switch(elementData.type) {
        case 'text':
            content.innerHTML = `<${elementData.properties.tag || 'p'}>${elementData.properties.content}</${elementData.properties.tag || 'p'}>`;
            break;
        case 'heading':
            content.innerHTML = `<h${elementData.properties.level || '2'}>${elementData.properties.content}</h${elementData.properties.level || '2'}>`;
            break;
        case 'link':
            content.innerHTML = `<a href="${elementData.properties.href}" target="${elementData.properties.target}">${elementData.properties.text}</a>`;
            break;
        case 'image':
            const imgAttrs = [];
            if (elementData.properties.src) imgAttrs.push(`src="${elementData.properties.src}"`);
            if (elementData.properties.alt) imgAttrs.push(`alt="${elementData.properties.alt}"`);
            if (elementData.properties.width) imgAttrs.push(`width="${elementData.properties.width}"`);
            if (elementData.properties.height) imgAttrs.push(`height="${elementData.properties.height}"`);
            
            if (elementData.properties.src) {
                content.innerHTML = `<img ${imgAttrs.join(' ')}>`;
            } else {
                content.innerHTML = '<div class="image-placeholder"><i class="fas fa-image"></i><br>图片占位符</div>';
            }
            break;
        case 'template-param':
            content.innerHTML = `<span class="template-param">{{{${elementData.properties.name}|${elementData.properties.default}}}}</span>`;
            break;
    }
}

// 复制元素
function duplicateElement(elementId) {
    const originalElement = document.getElementById(elementId);
    const originalData = designData.elements.find(el => el.id === elementId);
    
    if (!originalElement || !originalData) return;
    
    // 创建新元素
    elementCounter++;
    const newElementId = `element_${elementCounter}`;
    
    const newElement = originalElement.cloneNode(true);
    newElement.id = newElementId;
    
    // 更新控制按钮
    const controls = newElement.querySelector('.element-controls');
    controls.innerHTML = `
        <button class="element-control-btn duplicate" onclick="duplicateElement('${newElementId}')">
            <i class="fas fa-copy"></i>
        </button>
        <button class="element-control-btn delete" onclick="deleteElement('${newElementId}')">
            <i class="fas fa-trash"></i>
        </button>
    `;
    
    // 添加点击事件
    newElement.addEventListener('click', function(e) {
        e.stopPropagation();
        selectElement(this);
    });
    
    // 插入到原元素后面
    originalElement.parentNode.insertBefore(newElement, originalElement.nextSibling);
    
    // 复制数据
    const newElementData = {
        id: newElementId,
        type: originalData.type,
        properties: { ...originalData.properties },
        styles: { ...originalData.styles }
    };
    
    designData.elements.push(newElementData);
    
    // 选择新元素
    selectElement(newElement);
    updateCode();
}

// 删除元素
function deleteElement(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.remove();
        
        // 从数据中移除
        designData.elements = designData.elements.filter(el => el.id !== elementId);
        
        // 如果删除的是选中元素，清除选择
        if (selectedElement && selectedElement.id === elementId) {
            selectedElement = null;
            updatePropertiesPanel();
        }
        
        // 如果没有元素了，显示占位符
        if (designData.elements.length === 0) {
            const placeholder = designCanvas.querySelector('.canvas-placeholder');
            if (placeholder) {
                placeholder.style.display = 'block';
            }
        }
        
        updateCode();
    }
}

// 初始化标签页
function initializeTabs() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabPanes = document.querySelectorAll('.tab-pane');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            
            // 移除所有活动状态
            tabBtns.forEach(b => b.classList.remove('active'));
            tabPanes.forEach(p => p.classList.remove('active'));
            
            // 激活当前标签
            this.classList.add('active');
            document.getElementById(targetTab + '-tab').classList.add('active');
        });
    });
}

// 初始化事件监听器
function initializeEventListeners() {
    // 样式输入框事件
    const styleInputs = [
        'style-width', 'style-height', 'style-margin', 'style-padding',
        'style-font-size', 'style-color', 'style-background', 
        'style-border', 'style-border-radius'
    ];
    
    styleInputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (input) {
            input.addEventListener('input', function() {
                if (selectedElement) {
                    const property = inputId.replace('style-', '');
                    updateElementStyle(selectedElement.id, property, this.value);
                }
            });
        }
    });
    
    // 画布点击事件（取消选择）
    designCanvas.addEventListener('click', function(e) {
        if (e.target === this) {
            selectedElement = null;
            const previousSelected = document.querySelector('.design-element.selected');
            if (previousSelected) {
                previousSelected.classList.remove('selected');
            }
            updatePropertiesPanel();
        }
    });
    
    // 工具栏按钮事件
    document.getElementById('previewBtn').addEventListener('click', showPreview);
    document.getElementById('exportBtn').addEventListener('click', showExport);
    document.getElementById('clearBtn').addEventListener('click', clearCanvas);
}

// 初始化模态框
function initializeModals() {
    const modals = document.querySelectorAll('.modal');
    const closeButtons = document.querySelectorAll('.close');
    
    closeButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            modal.style.display = 'none';
        });
    });
    
    // 点击模态框外部关闭
    window.addEventListener('click', function(e) {
        modals.forEach(modal => {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });
    });
}

// 显示预览
function showPreview() {
    const modal = document.getElementById('previewModal');
    const previewContent = document.getElementById('preview-content');
    
    // 生成预览HTML
    const previewHTML = generatePreviewHTML();
    previewContent.innerHTML = previewHTML;
    
    modal.style.display = 'block';
}

// 显示导出
function showExport() {
    const modal = document.getElementById('exportModal');
    const mediawikiTextarea = document.getElementById('export-mediawiki');
    const cssTextarea = document.getElementById('export-css');
    
    // 生成代码
    const mediawikiCode = generateMediaWikiCode();
    const cssCode = generateCSSCode();
    
    mediawikiTextarea.value = mediawikiCode;
    cssTextarea.value = cssCode;
    
    modal.style.display = 'block';
}

// 清空画布
function clearCanvas() {
    if (confirm('确定要清空所有内容吗？此操作不可撤销。')) {
        designData.elements = [];
        selectedElement = null;
        
        // 清空画布
        const elements = designCanvas.querySelectorAll('.design-element');
        elements.forEach(el => el.remove());
        
        // 显示占位符
        const placeholder = designCanvas.querySelector('.canvas-placeholder');
        if (placeholder) {
            placeholder.style.display = 'block';
        }
        
        updatePropertiesPanel();
        updateCode();
    }
}

// 复制代码到剪贴板
function copyCode(textareaId) {
    const textarea = document.getElementById(textareaId);
    textarea.select();
    document.execCommand('copy');
    
    // 显示复制成功提示
    const btn = textarea.nextElementSibling;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-check"></i> 已复制';
    btn.style.backgroundColor = '#10b981';
    
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.style.backgroundColor = '';
    }, 2000);
}

// 更新代码显示
function updateCode() {
    const mediawikiCode = generateMediaWikiCode();
    const cssCode = generateCSSCode();
    
    const mediawikiTextarea = document.getElementById('mediawiki-code');
    const cssTextarea = document.getElementById('css-code');
    
    if (mediawikiTextarea) mediawikiTextarea.value = mediawikiCode;
    if (cssTextarea) cssTextarea.value = cssCode;
}
