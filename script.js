// 全局变量
let selectedElement = null;
let elementCounter = 0;
let designData = {
    elements: [],
    styles: {}
};

// DOM 元素
const designCanvas = document.getElementById('designCanvas');
const componentsPanel = document.querySelector('.components-panel');
const propertiesPanel = document.querySelector('.properties-panel');

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    initializeDragAndDrop();
    initializeTabs();
    initializeEventListeners();
    initializeModals();
});

// 初始化拖拽功能
function initializeDragAndDrop() {
    // 组件拖拽开始
    const componentItems = document.querySelectorAll('.component-item');
    componentItems.forEach(item => {
        item.addEventListener('dragstart', function(e) {
            e.dataTransfer.setData('text/plain', this.dataset.type);
            e.dataTransfer.setData('source', 'component');
            this.style.opacity = '0.5';
        });

        item.addEventListener('dragend', function(e) {
            this.style.opacity = '1';
        });
    });

    // 设计画布拖拽处理
    designCanvas.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('drag-over');

        // 显示插入指示器
        showDropIndicator(e);
    });

    designCanvas.addEventListener('dragleave', function(e) {
        if (!this.contains(e.relatedTarget)) {
            this.classList.remove('drag-over');
            hideDropIndicator();
        }
    });

    designCanvas.addEventListener('drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        this.classList.remove('drag-over');
        hideDropIndicator();

        const componentType = e.dataTransfer.getData('text/plain');
        const source = e.dataTransfer.getData('source');

        if (source === 'component') {
            // 获取插入位置
            const insertPosition = getInsertPosition(e);
            createElement(componentType, 0, 0, insertPosition);
        } else if (source === 'element') {
            // 移动现有元素
            const elementId = e.dataTransfer.getData('elementId');
            const insertPosition = getInsertPosition(e);
            moveElement(elementId, insertPosition);
        }
    });

    // 初始化现有元素的拖拽功能
    initializeElementDragAndDrop();
}

// 显示拖拽插入指示器
function showDropIndicator(e) {
    hideDropIndicator(); // 先隐藏之前的指示器

    const rect = designCanvas.getBoundingClientRect();
    const y = e.clientY - rect.top;

    // 找到最接近的插入位置
    const elements = Array.from(designCanvas.querySelectorAll('.design-element'));
    let insertAfter = null;

    for (let element of elements) {
        const elementRect = element.getBoundingClientRect();
        const elementY = elementRect.top - rect.top + elementRect.height / 2;

        if (y > elementY) {
            insertAfter = element;
        } else {
            break;
        }
    }

    // 创建插入指示器
    const indicator = document.createElement('div');
    indicator.className = 'drop-indicator';
    indicator.innerHTML = '<div class="drop-line"></div><span class="drop-text">在此处插入</span>';

    if (insertAfter) {
        insertAfter.parentNode.insertBefore(indicator, insertAfter.nextSibling);
    } else {
        // 插入到开头
        if (elements.length > 0) {
            designCanvas.insertBefore(indicator, elements[0]);
        } else {
            designCanvas.appendChild(indicator);
        }
    }
}

// 隐藏拖拽插入指示器
function hideDropIndicator() {
    const indicators = document.querySelectorAll('.drop-indicator');
    indicators.forEach(indicator => indicator.remove());
}

// 获取插入位置
function getInsertPosition(e) {
    const rect = designCanvas.getBoundingClientRect();
    const y = e.clientY - rect.top;

    const elements = Array.from(designCanvas.querySelectorAll('.design-element'));

    for (let i = 0; i < elements.length; i++) {
        const elementRect = elements[i].getBoundingClientRect();
        const elementY = elementRect.top - rect.top + elementRect.height / 2;

        if (y < elementY) {
            return { before: elements[i] };
        }
    }

    // 插入到末尾
    return { after: elements[elements.length - 1] || null };
}

// 初始化元素间拖拽功能
function initializeElementDragAndDrop() {
    // 这个函数会在创建新元素时被调用
}

// 创建设计元素
function createElement(type, x = 0, y = 0, insertPosition = null) {
    elementCounter++;
    const elementId = `element_${elementCounter}`;

    // 移除占位符
    const placeholder = designCanvas.querySelector('.canvas-placeholder');
    if (placeholder) {
        placeholder.style.display = 'none';
    }

    const element = document.createElement('div');
    element.className = 'design-element';
    element.id = elementId;
    element.dataset.type = type;

    // 添加控制按钮
    const controls = document.createElement('div');
    controls.className = 'element-controls';
    controls.innerHTML = `
        <button class="element-control-btn duplicate" onclick="duplicateElement('${elementId}')">
            <i class="fas fa-copy"></i>
        </button>
        <button class="element-control-btn delete" onclick="deleteElement('${elementId}')">
            <i class="fas fa-trash"></i>
        </button>
    `;
    element.appendChild(controls);

    // 添加调整大小手柄（仅对可调整大小的元素）
    if (isResizable(type)) {
        addResizeHandles(element);
    }

    // 根据类型创建内容
    const content = createElementContent(type, elementId);
    element.appendChild(content);

    // 添加点击事件
    element.addEventListener('click', function(e) {
        e.stopPropagation();
        selectElement(this);
    });

    // 添加双击编辑事件
    element.addEventListener('dblclick', function(e) {
        e.stopPropagation();
        startInlineEdit(this);
    });

    // 添加元素拖拽功能
    setupElementDragging(element);

    // 插入到指定位置
    if (insertPosition) {
        if (insertPosition.before) {
            designCanvas.insertBefore(element, insertPosition.before);
        } else if (insertPosition.after) {
            designCanvas.insertBefore(element, insertPosition.after.nextSibling);
        } else {
            designCanvas.appendChild(element);
        }
    } else {
        designCanvas.appendChild(element);
    }

    // 保存到设计数据
    const elementData = {
        id: elementId,
        type: type,
        properties: getDefaultProperties(type),
        styles: getDefaultStyles(type)
    };

    designData.elements.push(elementData);

    // 自动选择新创建的元素
    selectElement(element);

    // 更新代码
    updateCode();
}

// 移动元素到新位置
function moveElement(elementId, insertPosition) {
    const element = document.getElementById(elementId);
    if (!element) return;

    // 移动DOM元素
    if (insertPosition.before) {
        designCanvas.insertBefore(element, insertPosition.before);
    } else if (insertPosition.after) {
        designCanvas.insertBefore(element, insertPosition.after.nextSibling);
    } else {
        designCanvas.appendChild(element);
    }

    // 更新代码
    updateCode();
}

// 在容器内创建元素
function createElementInContainer(type, container) {
    elementCounter++;
    const elementId = `element_${elementCounter}`;

    const element = document.createElement('div');
    element.className = 'design-element nested-element';
    element.id = elementId;
    element.dataset.type = type;

    // 添加控制按钮
    const controls = document.createElement('div');
    controls.className = 'element-controls';
    controls.innerHTML = `
        <button class="element-control-btn duplicate" onclick="duplicateElement('${elementId}')">
            <i class="fas fa-copy"></i>
        </button>
        <button class="element-control-btn delete" onclick="deleteElement('${elementId}')">
            <i class="fas fa-trash"></i>
        </button>
    `;
    element.appendChild(controls);

    // 添加调整大小手柄（仅对可调整大小的元素）
    if (isResizable(type)) {
        addResizeHandles(element);
    }

    // 根据类型创建内容
    const content = createElementContent(type, elementId);
    element.appendChild(content);

    // 添加点击事件
    element.addEventListener('click', function(e) {
        e.stopPropagation();
        selectElement(this);
    });

    // 添加双击编辑事件
    element.addEventListener('dblclick', function(e) {
        e.stopPropagation();
        startInlineEdit(this);
    });

    // 添加元素拖拽功能
    setupElementDragging(element);

    // 清空容器占位符并添加元素
    if (container.querySelector('.container-placeholder, .row-placeholder, .column-placeholder')) {
        container.innerHTML = '';
    }
    container.appendChild(element);

    // 保存到设计数据
    const elementData = {
        id: elementId,
        type: type,
        properties: getDefaultProperties(type),
        styles: getDefaultStyles(type),
        parent: container.closest('.design-element')?.id || null
    };

    designData.elements.push(elementData);

    // 自动选择新创建的元素
    selectElement(element);

    // 更新代码
    updateCode();
}

// 设置元素拖拽功能
function setupElementDragging(element) {
    element.draggable = true;

    element.addEventListener('dragstart', function(e) {
        e.stopPropagation();
        e.dataTransfer.setData('text/plain', this.dataset.type);
        e.dataTransfer.setData('source', 'element');
        e.dataTransfer.setData('elementId', this.id);
        this.style.opacity = '0.5';
    });

    element.addEventListener('dragend', function(e) {
        this.style.opacity = '1';
    });
}

// 移动元素到容器
function moveElementToContainer(elementId, container) {
    const element = document.getElementById(elementId);
    if (!element) return;

    // 清空容器占位符
    if (container.querySelector('.container-placeholder, .row-placeholder, .column-placeholder')) {
        container.innerHTML = '';
    }

    // 移动元素
    container.appendChild(element);

    // 更新数据中的父级关系
    const elementData = designData.elements.find(el => el.id === elementId);
    if (elementData) {
        elementData.parent = container.closest('.design-element')?.id || null;
    }

    // 更新代码
    updateCode();
}

// 创建元素内容
function createElementContent(type, elementId) {
    const content = document.createElement('div');
    content.className = 'element-content';

    switch(type) {
        case 'text':
            content.innerHTML = '<p>这是一段文本内容，可以包含多行文字和段落格式。</p>';
            break;
        case 'heading':
            content.innerHTML = '<h2>这是标题</h2>';
            break;
        case 'image':
            content.innerHTML = '<div class="image-placeholder"><i class="fas fa-image"></i><br>图片占位符<br><small>点击编辑设置图片源</small></div>';
            break;
        case 'link':
            content.innerHTML = '<a href="#">这是一个链接</a>';
            break;
        case 'list':
            content.innerHTML = `
                <ul>
                    <li>列表项目 1</li>
                    <li>列表项目 2</li>
                    <li>列表项目 3</li>
                </ul>
            `;
            break;
        case 'quote':
            content.innerHTML = '<blockquote>这是一段引用文本，可以用来引用其他来源的内容。</blockquote>';
            break;
        case 'container':
            content.innerHTML = '<div class="container-placeholder">容器 - 拖拽其他元素到这里</div>';
            content.classList.add('drop-zone');
            setupContainerDropZone(content);
            break;
        case 'row':
            content.innerHTML = '<div class="row-placeholder">行布局 - 水平排列元素</div>';
            content.classList.add('drop-zone');
            setupContainerDropZone(content);
            break;
        case 'column':
            content.innerHTML = '<div class="column-placeholder">列布局 - 垂直排列元素</div>';
            content.classList.add('drop-zone');
            setupContainerDropZone(content);
            break;
        case 'table':
            content.innerHTML = `
                <table border="1" class="wikitable">
                    <tr><th>标题1</th><th>标题2</th><th>标题3</th></tr>
                    <tr><td>数据1</td><td>数据2</td><td>数据3</td></tr>
                    <tr><td>数据4</td><td>数据5</td><td>数据6</td></tr>
                </table>
            `;
            break;
        case 'tabs':
            content.innerHTML = `
                <div class="tabs-container">
                    <div class="tab-headers">
                        <div class="tab-header active">标签1</div>
                        <div class="tab-header">标签2</div>
                        <div class="tab-header">标签3</div>
                    </div>
                    <div class="tab-content">
                        <div class="tab-pane active">标签1的内容</div>
                        <div class="tab-pane">标签2的内容</div>
                        <div class="tab-pane">标签3的内容</div>
                    </div>
                </div>
            `;
            break;
        case 'collapsible':
            content.innerHTML = `
                <div class="collapsible">
                    <div class="collapsible-header">
                        <i class="fas fa-chevron-down"></i> 点击展开/折叠
                    </div>
                    <div class="collapsible-content">
                        这里是可折叠的内容区域，可以包含任何内容。
                    </div>
                </div>
            `;
            break;
        case 'infobox':
            content.innerHTML = `
                <div class="infobox">
                    <div class="infobox-title">信息框标题</div>
                    <div class="infobox-image">
                        <div class="image-placeholder"><i class="fas fa-image"></i></div>
                    </div>
                    <div class="infobox-content">
                        <div class="infobox-row">
                            <span class="label">姓名:</span>
                            <span class="value">示例名称</span>
                        </div>
                        <div class="infobox-row">
                            <span class="label">类型:</span>
                            <span class="value">示例类型</span>
                        </div>
                        <div class="infobox-row">
                            <span class="label">日期:</span>
                            <span class="value">2024年</span>
                        </div>
                    </div>
                </div>
            `;
            break;
        case 'navbox':
            content.innerHTML = `
                <div class="navbox">
                    <div class="navbox-title">导航框标题</div>
                    <div class="navbox-content">
                        <div class="navbox-group">
                            <div class="navbox-group-title">分组1</div>
                            <div class="navbox-links">
                                <a href="#">链接1</a> • <a href="#">链接2</a> • <a href="#">链接3</a>
                            </div>
                        </div>
                        <div class="navbox-group">
                            <div class="navbox-group-title">分组2</div>
                            <div class="navbox-links">
                                <a href="#">链接4</a> • <a href="#">链接5</a> • <a href="#">链接6</a>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            break;
        case 'template-param':
            content.innerHTML = '<span class="template-param">{{{参数名|默认值}}}</span>';
            break;
        case 'category':
            content.innerHTML = '<div class="category-tag">[[Category:示例分类]]</div>';
            break;
        case 'gallery':
            content.innerHTML = `
                <div class="gallery">
                    <div class="gallery-title">图片画廊</div>
                    <div class="gallery-grid">
                        <div class="gallery-item">
                            <div class="image-placeholder"><i class="fas fa-image"></i></div>
                            <div class="gallery-caption">图片1说明</div>
                        </div>
                        <div class="gallery-item">
                            <div class="image-placeholder"><i class="fas fa-image"></i></div>
                            <div class="gallery-caption">图片2说明</div>
                        </div>
                        <div class="gallery-item">
                            <div class="image-placeholder"><i class="fas fa-image"></i></div>
                            <div class="gallery-caption">图片3说明</div>
                        </div>
                    </div>
                </div>
            `;
            break;
        case 'notice':
            content.innerHTML = `
                <div class="notice notice-info">
                    <div class="notice-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="notice-content">
                        <div class="notice-title">提示</div>
                        <div class="notice-text">这是一个提示消息框，可以用来显示重要信息。</div>
                    </div>
                </div>
            `;
            break;
        default:
            content.innerHTML = '<div>未知组件类型</div>';
    }

    return content;
}

// 设置容器拖拽区域
function setupContainerDropZone(container) {
    container.addEventListener('dragover', function(e) {
        e.preventDefault();
        e.stopPropagation();
        this.classList.add('drag-over');
    });

    container.addEventListener('dragleave', function(e) {
        e.stopPropagation();
        if (!this.contains(e.relatedTarget)) {
            this.classList.remove('drag-over');
        }
    });

    container.addEventListener('drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        this.classList.remove('drag-over');

        const componentType = e.dataTransfer.getData('text/plain');
        const source = e.dataTransfer.getData('source');

        if (source === 'component') {
            // 在容器内创建新元素
            createElementInContainer(componentType, this);
        } else if (source === 'element') {
            // 移动现有元素到容器
            const elementId = e.dataTransfer.getData('elementId');
            moveElementToContainer(elementId, this);
        }
    });
}

// 获取默认属性
function getDefaultProperties(type) {
    const defaults = {
        text: {
            '内容': '这是一段文本内容，可以包含多行文字和段落格式。',
            '标签类型': 'p',
            '对齐方式': 'left'
        },
        heading: {
            '标题内容': '这是标题',
            '标题级别': '2',
            '对齐方式': 'left'
        },
        image: {
            '图片源': '',
            '替代文本': '图片描述',
            '宽度': '',
            '高度': '',
            '显示方式': 'thumb'
        },
        link: {
            '链接地址': '#',
            '显示文本': '这是一个链接',
            '打开方式': '_self',
            '链接类型': 'internal'
        },
        list: {
            '列表类型': 'ul',
            '列表项': '列表项目 1\n列表项目 2\n列表项目 3'
        },
        quote: {
            '引用内容': '这是一段引用文本，可以用来引用其他来源的内容。',
            '引用来源': '',
            '引用类型': 'blockquote'
        },
        container: {
            '容器类名': 'container',
            '容器标题': '',
            '宽度': '300',
            '高度': 'auto'
        },
        row: {
            '行类名': 'row',
            '对齐方式': 'left'
        },
        column: {
            '列类名': 'column',
            '列宽度': '12',
            '响应式': 'true'
        },
        table: {
            '行数': '3',
            '列数': '3',
            '表格样式': 'wikitable',
            '显示边框': 'true'
        },
        tabs: {
            '标签页数量': '3',
            '默认激活': '1',
            '标签标题': '标签1,标签2,标签3'
        },
        collapsible: {
            '标题': '点击展开/折叠',
            '默认状态': 'collapsed',
            '动画效果': 'true'
        },
        infobox: {
            '信息框标题': '信息框标题',
            '信息框类名': 'infobox',
            '宽度': '300',
            '高度': 'auto',
            '位置': 'right'
        },
        navbox: {
            '导航框标题': '导航框标题',
            '导航框类名': 'navbox',
            '折叠状态': 'expanded'
        },
        'template-param': {
            '参数名称': '参数名',
            '默认值': '默认值',
            '参数描述': ''
        },
        category: {
            '分类名称': '示例分类',
            '排序键': ''
        },
        gallery: {
            '画廊标题': '图片画廊',
            '每行图片数': '3',
            '图片大小': 'medium'
        },
        notice: {
            '提示类型': 'info',
            '标题': '提示',
            '内容': '这是一个提示消息框，可以用来显示重要信息。',
            '显示图标': 'true'
        }
    };

    return defaults[type] || {};
}

// 获取默认样式
function getDefaultStyles(type) {
    const defaults = {
        text: { 'font-size': '14px', 'color': '#000000' },
        heading: { 'font-size': '24px', 'font-weight': 'bold', 'color': '#000000' },
        image: { 'max-width': '100%', 'height': 'auto' },
        link: { 'color': '#0066cc', 'text-decoration': 'underline' },
        container: { 'padding': '10px', 'border': '1px solid #ccc' },
        row: { 'display': 'flex', 'flex-wrap': 'wrap' },
        column: { 'flex': '1', 'padding': '5px' },
        table: { 'border-collapse': 'collapse', 'width': '100%' },
        infobox: { 'border': '1px solid #aaa', 'background': '#f9f9f9', 'padding': '10px', 'width': '300px' },
        navbox: { 'border': '1px solid #aaa', 'background': '#f0f0f0', 'padding': '5px' },
        'template-param': { 'background': '#ffffcc', 'padding': '2px 4px', 'border-radius': '3px' }
    };
    
    return defaults[type] || {};
}

// 选择元素
function selectElement(element) {
    // 移除之前的选择
    const previousSelected = document.querySelector('.design-element.selected');
    if (previousSelected) {
        previousSelected.classList.remove('selected');
    }
    
    // 选择新元素
    element.classList.add('selected');
    selectedElement = element;
    
    // 更新属性面板
    updatePropertiesPanel();
    updateStylesPanel();
}

// 更新属性面板
function updatePropertiesPanel() {
    const noSelection = document.getElementById('no-selection');
    const elementProperties = document.getElementById('element-properties');

    if (!selectedElement) {
        noSelection.style.display = 'block';
        elementProperties.style.display = 'none';
        return;
    }

    noSelection.style.display = 'none';
    elementProperties.style.display = 'block';

    const elementData = designData.elements.find(el => el.id === selectedElement.id);
    if (!elementData) return;

    // 获取组件类型的中文名称
    const typeNames = {
        'text': '文本段落',
        'heading': '标题',
        'image': '图片',
        'link': '链接',
        'list': '列表',
        'quote': '引用',
        'container': '容器',
        'row': '行布局',
        'column': '列布局',
        'table': '表格',
        'tabs': '标签页',
        'collapsible': '折叠框',
        'infobox': '信息框',
        'navbox': '导航框',
        'template-param': '模板参数',
        'category': '分类标签',
        'gallery': '图片画廊',
        'notice': '提示框'
    };

    // 生成属性表单
    let propertiesHTML = `
        <div class="property-header">
            <h4><i class="fas fa-cog"></i> ${typeNames[elementData.type] || elementData.type}</h4>
            <small>编辑元素属性</small>
        </div>
    `;

    for (const [key, value] of Object.entries(elementData.properties)) {
        const inputType = getInputType(key, value);
        const inputElement = createPropertyInput(key, value, inputType, selectedElement.id);

        propertiesHTML += `
            <div class="property-row">
                <label class="property-label">
                    ${key}
                    ${getPropertyHelp(key)}
                </label>
                ${inputElement}
            </div>
        `;
    }

    elementProperties.innerHTML = propertiesHTML;

    // 绑定事件监听器到新创建的输入元素
    bindPropertyInputEvents();
}

// 绑定属性输入事件
function bindPropertyInputEvents() {
    const propertyInputs = document.querySelectorAll('#element-properties [data-property]');

    propertyInputs.forEach(input => {
        const property = input.dataset.property;
        const elementId = input.dataset.elementId;

        if (!property || !elementId) {
            console.warn('Missing property or elementId data attributes:', input);
            return;
        }

        // 移除之前的事件监听器（如果存在）
        input.removeEventListener('input', input._propertyInputHandler);
        input.removeEventListener('change', input._propertyChangeHandler);

        // 创建事件处理器
        const inputHandler = function(e) {
            console.log('Property input event:', elementId, property, this.value);
            updateElementProperty(elementId, property, this.value);
        };

        const changeHandler = function(e) {
            console.log('Property change event:', elementId, property, this.value);
            updateElementProperty(elementId, property, this.value);
        };

        // 保存处理器引用以便后续移除
        input._propertyInputHandler = inputHandler;
        input._propertyChangeHandler = changeHandler;

        // 绑定事件
        input.addEventListener('input', inputHandler);
        input.addEventListener('change', changeHandler);

        // 对于select元素，添加特殊处理
        if (input.tagName.toLowerCase() === 'select') {
            input.addEventListener('change', function(e) {
                if (this.value === '__custom__') {
                    // 显示自定义输入框
                    const customInput = this.parentNode.querySelector('.custom-input');
                    if (customInput) {
                        this.style.display = 'none';
                        customInput.style.display = 'block';
                        customInput.focus();
                    }
                } else {
                    changeHandler.call(this, e);
                }
            });
        }

        // 处理自定义输入框
        if (input.classList.contains('custom-input')) {
            input.addEventListener('blur', function(e) {
                const select = this.parentNode.querySelector('.property-select');
                if (this.value.trim()) {
                    // 保存自定义值
                    updateElementProperty(elementId, property, this.value);
                }
                // 恢复选择框显示
                this.style.display = 'none';
                select.style.display = 'block';
            });

            input.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    this.blur();
                } else if (e.key === 'Escape') {
                    this.value = '';
                    this.blur();
                }
            });
        }
    });
}

// 内联编辑功能
let currentInlineEdit = null;

function startInlineEdit(element) {
    // 如果已经有元素在编辑中，先结束编辑
    if (currentInlineEdit) {
        endInlineEdit(true);
    }

    const elementData = designData.elements.find(el => el.id === element.id);
    if (!elementData) return;

    const content = element.querySelector('.element-content');
    if (!content) return;

    // 根据元素类型创建合适的编辑器
    const editableProperty = getEditableProperty(elementData.type);
    if (!editableProperty) return;

    const currentValue = elementData.properties[editableProperty] || '';

    // 创建编辑器
    const editor = createInlineEditor(elementData.type, currentValue);

    // 保存编辑状态
    currentInlineEdit = {
        element: element,
        content: content,
        originalContent: content.innerHTML,
        property: editableProperty,
        editor: editor
    };

    // 替换内容为编辑器
    content.innerHTML = '';
    content.appendChild(editor);

    // 添加编辑模式样式
    element.classList.add('inline-editing');

    // 聚焦编辑器
    editor.focus();
    if (editor.select) editor.select();
}

function createInlineEditor(elementType, currentValue) {
    let editor;

    switch (elementType) {
        case 'text':
        case 'quote':
            editor = document.createElement('textarea');
            editor.value = currentValue;
            editor.rows = 3;
            break;

        case 'heading':
        case 'link':
        case 'template-param':
        case 'category':
            editor = document.createElement('input');
            editor.type = 'text';
            editor.value = currentValue;
            break;

        case 'list':
            editor = document.createElement('textarea');
            editor.value = currentValue;
            editor.rows = 5;
            break;

        case 'image':
            editor = document.createElement('input');
            editor.type = 'text';
            editor.value = currentValue;
            editor.placeholder = '输入图片文件名，如：Example.jpg';
            break;

        default:
            editor = document.createElement('input');
            editor.type = 'text';
            editor.value = currentValue;
            break;
    }

    // 添加样式
    editor.className = 'inline-editor';

    // 绑定事件
    editor.addEventListener('keydown', handleInlineEditorKeydown);
    editor.addEventListener('blur', () => endInlineEdit(true));

    return editor;
}

function getEditableProperty(elementType) {
    const editableProperties = {
        'text': '内容',
        'heading': '标题内容',
        'image': '图片源',
        'link': '显示文本',
        'list': '列表项',
        'quote': '引用内容',
        'template-param': '参数名称',
        'category': '分类名称',
        'notice': '内容'
    };

    return editableProperties[elementType];
}

function handleInlineEditorKeydown(e) {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        endInlineEdit(true);
    } else if (e.key === 'Escape') {
        e.preventDefault();
        endInlineEdit(false);
    }
}

function endInlineEdit(save) {
    if (!currentInlineEdit) return;

    const { element, content, originalContent, property, editor } = currentInlineEdit;

    if (save) {
        // 保存更改
        const newValue = editor.value;
        updateElementProperty(element.id, property, newValue);
    } else {
        // 恢复原内容
        content.innerHTML = originalContent;
    }

    // 移除编辑模式样式
    element.classList.remove('inline-editing');

    // 清除编辑状态
    currentInlineEdit = null;
}

// 调整大小功能
let currentResize = null;

function isResizable(elementType) {
    const resizableTypes = ['image', 'container', 'table', 'gallery', 'infobox'];
    return resizableTypes.includes(elementType);
}

function addResizeHandles(element) {
    const handles = document.createElement('div');
    handles.className = 'resize-handles';
    handles.innerHTML = `
        <div class="resize-handle resize-handle-nw" data-direction="nw"></div>
        <div class="resize-handle resize-handle-n" data-direction="n"></div>
        <div class="resize-handle resize-handle-ne" data-direction="ne"></div>
        <div class="resize-handle resize-handle-w" data-direction="w"></div>
        <div class="resize-handle resize-handle-e" data-direction="e"></div>
        <div class="resize-handle resize-handle-sw" data-direction="sw"></div>
        <div class="resize-handle resize-handle-s" data-direction="s"></div>
        <div class="resize-handle resize-handle-se" data-direction="se"></div>
    `;

    element.appendChild(handles);

    // 绑定调整大小事件
    handles.querySelectorAll('.resize-handle').forEach(handle => {
        handle.addEventListener('mousedown', startResize);
    });
}

function startResize(e) {
    e.preventDefault();
    e.stopPropagation();

    const handle = e.target;
    const element = handle.closest('.design-element');
    const direction = handle.dataset.direction;

    if (!element) return;

    const rect = element.getBoundingClientRect();
    const canvasRect = designCanvas.getBoundingClientRect();

    currentResize = {
        element: element,
        direction: direction,
        startX: e.clientX,
        startY: e.clientY,
        startWidth: rect.width,
        startHeight: rect.height,
        startLeft: rect.left - canvasRect.left,
        startTop: rect.top - canvasRect.top
    };

    // 添加调整大小样式
    element.classList.add('resizing');
    document.body.classList.add('resizing');

    // 创建尺寸提示
    showResizeTooltip(element, rect.width, rect.height);

    // 绑定全局事件
    document.addEventListener('mousemove', handleResize);
    document.addEventListener('mouseup', endResize);
}

function handleResize(e) {
    if (!currentResize) return;

    const { element, direction, startX, startY, startWidth, startHeight, startLeft, startTop } = currentResize;

    const deltaX = e.clientX - startX;
    const deltaY = e.clientY - startY;

    let newWidth = startWidth;
    let newHeight = startHeight;
    let newLeft = startLeft;
    let newTop = startTop;

    // 根据方向计算新尺寸
    switch (direction) {
        case 'nw':
            newWidth = Math.max(50, startWidth - deltaX);
            newHeight = Math.max(30, startHeight - deltaY);
            newLeft = startLeft + (startWidth - newWidth);
            newTop = startTop + (startHeight - newHeight);
            break;
        case 'n':
            newHeight = Math.max(30, startHeight - deltaY);
            newTop = startTop + (startHeight - newHeight);
            break;
        case 'ne':
            newWidth = Math.max(50, startWidth + deltaX);
            newHeight = Math.max(30, startHeight - deltaY);
            newTop = startTop + (startHeight - newHeight);
            break;
        case 'w':
            newWidth = Math.max(50, startWidth - deltaX);
            newLeft = startLeft + (startWidth - newWidth);
            break;
        case 'e':
            newWidth = Math.max(50, startWidth + deltaX);
            break;
        case 'sw':
            newWidth = Math.max(50, startWidth - deltaX);
            newHeight = Math.max(30, startHeight + deltaY);
            newLeft = startLeft + (startWidth - newWidth);
            break;
        case 's':
            newHeight = Math.max(30, startHeight + deltaY);
            break;
        case 'se':
            newWidth = Math.max(50, startWidth + deltaX);
            newHeight = Math.max(30, startHeight + deltaY);
            break;
    }

    // 应用新尺寸
    element.style.width = newWidth + 'px';
    element.style.height = newHeight + 'px';

    // 更新尺寸提示
    updateResizeTooltip(newWidth, newHeight);

    // 更新属性面板
    updateElementSizeProperties(element.id, newWidth, newHeight);
}

function endResize() {
    if (!currentResize) return;

    const { element } = currentResize;

    // 移除调整大小样式
    element.classList.remove('resizing');
    document.body.classList.remove('resizing');

    // 隐藏尺寸提示
    hideResizeTooltip();

    // 移除全局事件
    document.removeEventListener('mousemove', handleResize);
    document.removeEventListener('mouseup', endResize);

    // 更新代码
    updateCode();

    currentResize = null;
}

function showResizeTooltip(element, width, height) {
    let tooltip = document.getElementById('resize-tooltip');
    if (!tooltip) {
        tooltip = document.createElement('div');
        tooltip.id = 'resize-tooltip';
        tooltip.className = 'resize-tooltip';
        document.body.appendChild(tooltip);
    }

    tooltip.textContent = `${Math.round(width)} × ${Math.round(height)}`;
    tooltip.style.display = 'block';

    const rect = element.getBoundingClientRect();
    tooltip.style.left = (rect.left + rect.width / 2 - tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = (rect.top - tooltip.offsetHeight - 10) + 'px';
}

function updateResizeTooltip(width, height) {
    const tooltip = document.getElementById('resize-tooltip');
    if (tooltip) {
        tooltip.textContent = `${Math.round(width)} × ${Math.round(height)}`;
    }
}

function hideResizeTooltip() {
    const tooltip = document.getElementById('resize-tooltip');
    if (tooltip) {
        tooltip.style.display = 'none';
    }
}

function updateElementSizeProperties(elementId, width, height) {
    const elementData = designData.elements.find(el => el.id === elementId);
    if (elementData) {
        elementData.properties['宽度'] = Math.round(width);
        elementData.properties['高度'] = Math.round(height);

        // 如果当前选中的是这个元素，更新属性面板
        if (selectedElement && selectedElement.id === elementId) {
            updatePropertiesPanel();
        }
    }
}

// 获取输入框类型
function getInputType(key, value) {
    // 颜色选择器
    if (key.includes('颜色') || key.includes('color')) return 'color';

    // 数字输入
    if (key.includes('数量') || key.includes('级别') || key.includes('宽度') || key.includes('高度') ||
        key.includes('行数') || key.includes('列数') || key.includes('大小')) return 'number';

    // 多行文本
    if (key.includes('内容') || key.includes('项') || key.includes('描述') ||
        (typeof value === 'string' && value.includes('\n'))) return 'textarea';

    // 下拉选择
    if (key.includes('类型') || key.includes('方式') || key.includes('状态') ||
        key.includes('级别') || key.includes('对齐') || key.includes('显示') ||
        key.includes('位置') || key.includes('打开')) return 'select';

    return 'text';
}

// 创建属性输入元素
function createPropertyInput(key, value, type, elementId) {
    // 确保值被正确转义以防止HTML注入
    const escapedValue = String(value).replace(/"/g, '&quot;').replace(/'/g, '&#39;');
    const escapedKey = String(key).replace(/"/g, '&quot;').replace(/'/g, '&#39;');

    // 为了避免内联事件处理器的问题，我们将使用数据属性并在后面绑定事件
    switch (type) {
        case 'textarea':
            return `<textarea data-property="${escapedKey}" data-element-id="${elementId}" rows="3">${value}</textarea>`;
        case 'number':
            return `<input type="number" data-property="${escapedKey}" data-element-id="${elementId}" value="${escapedValue}">`;
        case 'color':
            return `<input type="color" data-property="${escapedKey}" data-element-id="${elementId}" value="${escapedValue}">`;
        case 'select':
            return createSelectInput(key, value, elementId);
        default:
            return `<input type="text" data-property="${escapedKey}" data-element-id="${elementId}" value="${escapedValue}">`;
    }
}

// 创建选择框输入
function createSelectInput(key, value, elementId) {
    const options = getSelectOptions(key);
    const escapedKey = String(key).replace(/"/g, '&quot;').replace(/'/g, '&#39;');

    // 检查是否允许自定义值
    const allowCustom = isCustomValueAllowed(key);

    let selectHTML = `<div class="select-container">`;
    selectHTML += `<select data-property="${escapedKey}" data-element-id="${elementId}" class="property-select">`;

    options.forEach(option => {
        const selected = option.value === value ? 'selected' : '';
        selectHTML += `<option value="${option.value}" ${selected}>${option.label}</option>`;
    });

    // 如果当前值不在选项中，添加自定义选项
    const hasCurrentValue = options.some(option => option.value === value);
    if (!hasCurrentValue && value) {
        selectHTML += `<option value="${value}" selected>自定义: ${value}</option>`;
    }

    if (allowCustom) {
        selectHTML += `<option value="__custom__">自定义...</option>`;
    }

    selectHTML += '</select>';

    if (allowCustom) {
        selectHTML += `<input type="text" class="custom-input" data-property="${escapedKey}" data-element-id="${elementId}"
                      style="display: none;" placeholder="输入自定义值">`;
    }

    selectHTML += '</div>';
    return selectHTML;
}

// 检查是否允许自定义值
function isCustomValueAllowed(key) {
    const customAllowed = [
        '标签类型', '对齐方式', '显示方式', '链接类型',
        '列表类型', '引用类型', '提示类型', '位置'
    ];
    return customAllowed.includes(key);
}

// 获取选择框选项
function getSelectOptions(key) {
    const optionsMap = {
        '标签类型': [
            { value: 'p', label: '段落 (p) - 普通文本段落' },
            { value: 'div', label: '区块 (div) - 通用容器' },
            { value: 'span', label: '行内 (span) - 行内元素' },
            { value: 'strong', label: '粗体 (strong) - 重要文本' },
            { value: 'em', label: '斜体 (em) - 强调文本' },
            { value: 'code', label: '代码 (code) - 内联代码' }
        ],
        '标题级别': [
            { value: '1', label: 'H1 - 页面主标题（最大）' },
            { value: '2', label: 'H2 - 章节标题（常用）' },
            { value: '3', label: 'H3 - 子章节标题' },
            { value: '4', label: 'H4 - 小节标题' },
            { value: '5', label: 'H5 - 子小节标题' },
            { value: '6', label: 'H6 - 最小标题' }
        ],
        '对齐方式': [
            { value: 'left', label: '左对齐 - 默认对齐方式' },
            { value: 'center', label: '居中对齐 - 标题常用' },
            { value: 'right', label: '右对齐 - 特殊用途' },
            { value: 'justify', label: '两端对齐 - 段落文本' }
        ],
        '显示方式': [
            { value: 'thumb', label: '缩略图 - 带边框和说明' },
            { value: 'frame', label: '带框 - 完整尺寸带边框' },
            { value: 'frameless', label: '无框 - 完整尺寸无边框' },
            { value: 'border', label: '边框 - 仅显示边框' },
            { value: 'none', label: '无样式 - 原始显示' }
        ],
        '打开方式': [
            { value: '_self', label: '当前窗口 - 在同一页面打开' },
            { value: '_blank', label: '新窗口 - 在新标签页打开' },
            { value: '_parent', label: '父窗口 - 在父框架打开' },
            { value: '_top', label: '顶层窗口 - 在最顶层打开' }
        ],
        '链接类型': [
            { value: 'internal', label: '内部链接 - 站内页面链接' },
            { value: 'external', label: '外部链接 - 站外网址链接' },
            { value: 'file', label: '文件链接 - 文件下载链接' },
            { value: 'mailto', label: '邮件链接 - 电子邮件地址' }
        ],
        '列表类型': [
            { value: 'ul', label: '无序列表 - 项目符号列表' },
            { value: 'ol', label: '有序列表 - 数字编号列表' },
            { value: 'dl', label: '定义列表 - 术语定义列表' }
        ],
        '引用类型': [
            { value: 'blockquote', label: '块引用 - 段落引用' },
            { value: 'code', label: '代码块 - 程序代码' },
            { value: 'pre', label: '预格式化 - 保持格式' },
            { value: 'cite', label: '引用来源 - 引用标注' }
        ],
        '提示类型': [
            { value: 'info', label: '信息 - 一般信息提示' },
            { value: 'warning', label: '警告 - 注意事项提示' },
            { value: 'error', label: '错误 - 错误信息提示' },
            { value: 'success', label: '成功 - 成功操作提示' },
            { value: 'note', label: '备注 - 补充说明' },
            { value: 'tip', label: '提示 - 使用技巧' }
        ],
        '默认状态': [
            { value: 'collapsed', label: '折叠 - 默认收起状态' },
            { value: 'expanded', label: '展开 - 默认展开状态' }
        ],
        '位置': [
            { value: 'left', label: '左侧 - 靠左显示' },
            { value: 'center', label: '居中 - 居中显示' },
            { value: 'right', label: '右侧 - 靠右显示' },
            { value: 'top', label: '顶部 - 上方显示' },
            { value: 'bottom', label: '底部 - 下方显示' }
        ],
        '表格样式': [
            { value: 'wikitable', label: 'wikitable - 标准维基表格' },
            { value: 'sortable', label: 'sortable - 可排序表格' },
            { value: 'collapsible', label: 'collapsible - 可折叠表格' },
            { value: 'bordered', label: 'bordered - 带边框表格' }
        ],
        '容器类型': [
            { value: 'infobox', label: 'infobox - 信息框容器' },
            { value: 'navbox', label: 'navbox - 导航框容器' },
            { value: 'sidebar', label: 'sidebar - 侧边栏容器' },
            { value: 'content', label: 'content - 内容容器' }
        ]
    };

    return optionsMap[key] || [{ value: '', label: '请选择选项' }];
}

// 获取属性帮助文本
function getPropertyHelp(key) {
    const helpTexts = {
        '宽度': '<small class="help-text">设置元素宽度，可以是像素值(如300)或百分比(如100%)</small>',
        '高度': '<small class="help-text">设置元素高度，可以是像素值或auto自动</small>',
        '标题级别': '<small class="help-text">H1最大，H6最小，H2常用于章节标题</small>',
        '显示方式': '<small class="help-text">thumb显示缩略图，frame显示完整尺寸</small>',
        '对齐方式': '<small class="help-text">控制文本或元素的对齐方式</small>',
        '提示类型': '<small class="help-text">不同类型显示不同颜色和图标</small>',
        '位置': '<small class="help-text">控制元素在页面中的位置</small>'
    };

    return helpTexts[key] || '';
}

// 获取属性帮助信息
function getPropertyHelp(key) {
    const helpMap = {
        '内容': '<small>输入文本内容</small>',
        '标题内容': '<small>输入标题文字</small>',
        '图片源': '<small>输入图片文件名，如：Example.jpg</small>',
        '链接地址': '<small>内部链接用页面名，外部链接用完整URL</small>',
        '参数名称': '<small>模板参数的名称</small>',
        '分类名称': '<small>页面所属的分类</small>',
        '列表项': '<small>每行一个列表项</small>'
    };

    return helpMap[key] || '';
}

// 更新样式面板
function updateStylesPanel() {
    if (!selectedElement) return;
    
    const elementData = designData.elements.find(el => el.id === selectedElement.id);
    if (!elementData) return;
    
    // 填充样式输入框
    document.getElementById('style-width').value = elementData.styles.width || '';
    document.getElementById('style-height').value = elementData.styles.height || '';
    document.getElementById('style-margin').value = elementData.styles.margin || '';
    document.getElementById('style-padding').value = elementData.styles.padding || '';
    document.getElementById('style-font-size').value = elementData.styles['font-size'] || '';
    document.getElementById('style-color').value = elementData.styles.color || '#000000';
    document.getElementById('style-background').value = elementData.styles.background || '#ffffff';
    document.getElementById('style-border').value = elementData.styles.border || '';
    document.getElementById('style-border-radius').value = elementData.styles['border-radius'] || '';
}

// 更新元素属性
function updateElementProperty(elementId, property, value) {
    console.log('Updating property:', elementId, property, value); // Debug log

    // 验证输入参数
    if (!elementId || !property) {
        console.error('Invalid parameters for updateElementProperty:', { elementId, property, value });
        return false;
    }

    const elementData = designData.elements.find(el => el.id === elementId);
    if (!elementData) {
        console.error('Element data not found for ID:', elementId);
        console.log('Available elements:', designData.elements.map(el => ({ id: el.id, type: el.type })));
        return false;
    }

    // 保存旧值用于调试
    const oldValue = elementData.properties[property];

    try {
        // 更新属性值
        elementData.properties[property] = value;

        console.log(`Property "${property}" updated from "${oldValue}" to "${value}" for element ${elementId}`);

        // 更新DOM元素
        const domUpdateSuccess = updateElementDOM(elementId);
        if (domUpdateSuccess === false) {
            console.error('Failed to update DOM for element:', elementId);
            return false;
        }

        // 更新代码
        updateCode();

        console.log('Property update completed successfully');
        return true;

    } catch (error) {
        console.error('Error updating element property:', error);
        // 恢复旧值
        elementData.properties[property] = oldValue;
        return false;
    }
}

// 更新元素样式
function updateElementStyle(elementId, property, value) {
    const elementData = designData.elements.find(el => el.id === elementId);
    if (elementData) {
        elementData.styles[property] = value;
        
        // 应用样式到DOM元素
        const element = document.getElementById(elementId);
        if (element) {
            element.style[property] = value;
        }
        
        updateCode();
    }
}

// 更新DOM元素内容
function updateElementDOM(elementId) {
    console.log('Updating DOM for element:', elementId); // Debug log

    const element = document.getElementById(elementId);
    const elementData = designData.elements.find(el => el.id === elementId);

    if (!element) {
        console.error('DOM element not found for ID:', elementId);
        return false;
    }

    if (!elementData) {
        console.error('Element data not found for ID:', elementId);
        return false;
    }

    const content = element.querySelector('.element-content');
    if (!content) {
        console.error('Element content container not found for:', elementId);
        return false;
    }

    console.log('Updating content for type:', elementData.type, 'with properties:', elementData.properties); // Debug log

    try {

    // 根据类型和属性更新内容
    switch(elementData.type) {
        case 'text':
            const textContent = elementData.properties['内容'] || elementData.properties.content || '文本内容';
            const textTag = elementData.properties['标签类型'] || elementData.properties.tag || 'p';
            content.innerHTML = `<${textTag}>${textContent}</${textTag}>`;
            break;
        case 'heading':
            const headingContent = elementData.properties['标题内容'] || elementData.properties.content || '标题';
            const headingLevel = elementData.properties['标题级别'] || elementData.properties.level || '2';
            content.innerHTML = `<h${headingLevel}>${headingContent}</h${headingLevel}>`;
            break;
        case 'link':
            const linkHref = elementData.properties['链接地址'] || elementData.properties.href || '#';
            const linkText = elementData.properties['显示文本'] || elementData.properties.text || '链接';
            const linkTarget = elementData.properties['打开方式'] || elementData.properties.target || '_self';
            content.innerHTML = `<a href="${linkHref}" target="${linkTarget}">${linkText}</a>`;
            break;
        case 'image':
            const imgSrc = elementData.properties['图片源'] || elementData.properties.src || '';
            const imgAlt = elementData.properties['替代文本'] || elementData.properties.alt || '图片描述';
            const imgWidth = elementData.properties['宽度'] || elementData.properties.width || '';
            const imgHeight = elementData.properties['高度'] || elementData.properties.height || '';

            if (imgSrc) {
                const imgAttrs = [`src="${imgSrc}"`, `alt="${imgAlt}"`];
                if (imgWidth) imgAttrs.push(`width="${imgWidth}"`);
                if (imgHeight) imgAttrs.push(`height="${imgHeight}"`);
                content.innerHTML = `<img ${imgAttrs.join(' ')}>`;
            } else {
                content.innerHTML = '<div class="image-placeholder"><i class="fas fa-image"></i><br>图片占位符<br><small>点击编辑设置图片源</small></div>';
            }
            break;
        case 'list':
            const listType = elementData.properties['列表类型'] || elementData.properties.listType || 'ul';
            const listItems = (elementData.properties['列表项'] || elementData.properties.items || '列表项目 1\n列表项目 2\n列表项目 3').split('\n');
            let listHTML = `<${listType}>`;
            listItems.forEach(item => {
                if (item.trim()) {
                    listHTML += `<li>${item.trim()}</li>`;
                }
            });
            listHTML += `</${listType}>`;
            content.innerHTML = listHTML;
            break;
        case 'quote':
            const quoteContent = elementData.properties['引用内容'] || elementData.properties.content || '引用文本';
            const quoteSource = elementData.properties['引用来源'] || elementData.properties.source || '';
            let quoteHTML = `<blockquote>${quoteContent}`;
            if (quoteSource) {
                quoteHTML += `<cite>—— ${quoteSource}</cite>`;
            }
            quoteHTML += '</blockquote>';
            content.innerHTML = quoteHTML;
            break;
        case 'template-param':
            const paramName = elementData.properties['参数名称'] || elementData.properties.name || '参数名';
            const paramDefault = elementData.properties['默认值'] || elementData.properties.default || '默认值';
            content.innerHTML = `<span class="template-param">{{{${paramName}|${paramDefault}}}}</span>`;
            break;
        case 'category':
            const categoryName = elementData.properties['分类名称'] || elementData.properties.name || '示例分类';
            content.innerHTML = `<div class="category-tag">[[Category:${categoryName}]]</div>`;
            break;
        case 'notice':
            const noticeType = elementData.properties['提示类型'] || elementData.properties.type || 'info';
            const noticeTitle = elementData.properties['标题'] || elementData.properties.title || '提示';
            const noticeContent = elementData.properties['内容'] || elementData.properties.content || '提示内容';
            content.innerHTML = `
                <div class="notice notice-${noticeType}">
                    <div class="notice-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="notice-content">
                        <div class="notice-title">${noticeTitle}</div>
                        <div class="notice-text">${noticeContent}</div>
                    </div>
                </div>
            `;
            break;
        default:
            console.log('No specific update handler for type:', elementData.type); // Debug log
            break;
    }

    console.log('DOM update completed for:', elementId); // Debug log
    return true;

    } catch (error) {
        console.error('Error updating DOM for element:', elementId, error);
        return false;
    }
}

// 复制元素
function duplicateElement(elementId) {
    const originalElement = document.getElementById(elementId);
    const originalData = designData.elements.find(el => el.id === elementId);

    if (!originalElement || !originalData) return;

    // 创建新元素
    elementCounter++;
    const newElementId = `element_${elementCounter}`;

    const newElement = originalElement.cloneNode(true);
    newElement.id = newElementId;

    // 更新控制按钮
    const controls = newElement.querySelector('.element-controls');
    controls.innerHTML = `
        <button class="element-control-btn duplicate" onclick="duplicateElement('${newElementId}')">
            <i class="fas fa-copy"></i>
        </button>
        <button class="element-control-btn delete" onclick="deleteElement('${newElementId}')">
            <i class="fas fa-trash"></i>
        </button>
    `;

    // 添加点击事件
    newElement.addEventListener('click', function(e) {
        e.stopPropagation();
        selectElement(this);
    });

    // 重新设置拖拽功能
    setupElementDragging(newElement);

    // 如果是容器类型，重新设置拖拽区域
    const content = newElement.querySelector('.element-content');
    if (content && (originalData.type === 'container' || originalData.type === 'row' || originalData.type === 'column')) {
        setupContainerDropZone(content);
    }

    // 插入到原元素后面
    originalElement.parentNode.insertBefore(newElement, originalElement.nextSibling);

    // 复制数据
    const newElementData = {
        id: newElementId,
        type: originalData.type,
        properties: { ...originalData.properties },
        styles: { ...originalData.styles },
        parent: originalData.parent
    };

    designData.elements.push(newElementData);

    // 选择新元素
    selectElement(newElement);
    updateCode();
}

// 删除元素
function deleteElement(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.remove();
        
        // 从数据中移除
        designData.elements = designData.elements.filter(el => el.id !== elementId);
        
        // 如果删除的是选中元素，清除选择
        if (selectedElement && selectedElement.id === elementId) {
            selectedElement = null;
            updatePropertiesPanel();
        }
        
        // 如果没有元素了，显示占位符
        if (designData.elements.length === 0) {
            const placeholder = designCanvas.querySelector('.canvas-placeholder');
            if (placeholder) {
                placeholder.style.display = 'block';
            }
        }
        
        updateCode();
    }
}

// 初始化标签页
function initializeTabs() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabPanes = document.querySelectorAll('.tab-pane');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            
            // 移除所有活动状态
            tabBtns.forEach(b => b.classList.remove('active'));
            tabPanes.forEach(p => p.classList.remove('active'));
            
            // 激活当前标签
            this.classList.add('active');
            document.getElementById(targetTab + '-tab').classList.add('active');
        });
    });
}

// 初始化事件监听器
function initializeEventListeners() {
    // 样式输入框事件
    const styleInputs = [
        'style-width', 'style-height', 'style-margin', 'style-padding',
        'style-font-size', 'style-color', 'style-background',
        'style-border', 'style-border-radius'
    ];

    styleInputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (input) {
            input.addEventListener('input', function() {
                if (selectedElement) {
                    const property = inputId.replace('style-', '');
                    updateElementStyle(selectedElement.id, property, this.value);
                }
            });
        }
    });

    // 画布点击事件（取消选择）
    designCanvas.addEventListener('click', function(e) {
        if (e.target === this) {
            selectedElement = null;
            const previousSelected = document.querySelector('.design-element.selected');
            if (previousSelected) {
                previousSelected.classList.remove('selected');
            }
            updatePropertiesPanel();
        }
    });

    // 工具栏按钮事件
    document.getElementById('previewBtn').addEventListener('click', showPreview);
    document.getElementById('exportBtn').addEventListener('click', showExport);
    document.getElementById('clearBtn').addEventListener('click', clearCanvas);
    document.getElementById('aiAssistantBtn').addEventListener('click', showAIAssistant);
    document.getElementById('aiConfigBtn').addEventListener('click', showAIConfig);

    // 添加拖拽提示
    addDragHints();
}

// 添加拖拽提示
function addDragHints() {
    // 为组件添加提示
    const componentItems = document.querySelectorAll('.component-item');
    componentItems.forEach(item => {
        item.title = '拖拽到画布或容器中';
    });

    // 监听第一次拖拽
    let firstDrag = true;
    designCanvas.addEventListener('dragenter', function() {
        if (firstDrag) {
            showDragHint();
            firstDrag = false;
        }
    });
}

// 显示拖拽提示
function showDragHint() {
    const hint = document.createElement('div');
    hint.className = 'drag-hint';
    hint.innerHTML = `
        <div class="hint-content">
            <i class="fas fa-info-circle"></i>
            <p><strong>拖拽提示：</strong></p>
            <ul>
                <li>拖拽组件到画布创建新元素</li>
                <li>拖拽组件到容器、行、列中嵌套元素</li>
                <li>拖拽现有元素可以移动位置</li>
                <li>点击元素可以编辑属性和样式</li>
            </ul>
            <button onclick="closeDragHint()">知道了</button>
        </div>
    `;

    document.body.appendChild(hint);

    // 3秒后自动关闭
    setTimeout(() => {
        if (hint.parentNode) {
            hint.remove();
        }
    }, 8000);
}

// 关闭拖拽提示
function closeDragHint() {
    const hint = document.querySelector('.drag-hint');
    if (hint) {
        hint.remove();
    }
}

// 初始化模态框
function initializeModals() {
    const modals = document.querySelectorAll('.modal');
    const closeButtons = document.querySelectorAll('.close');
    
    closeButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            modal.style.display = 'none';
        });
    });
    
    // 点击模态框外部关闭
    window.addEventListener('click', function(e) {
        modals.forEach(modal => {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });
    });
}

// 显示预览
function showPreview() {
    const modal = document.getElementById('previewModal');
    const previewContent = document.getElementById('preview-content');
    
    // 生成预览HTML
    const previewHTML = generatePreviewHTML();
    previewContent.innerHTML = previewHTML;
    
    modal.style.display = 'block';
}

// 显示导出
function showExport() {
    const modal = document.getElementById('exportModal');
    const mediawikiTextarea = document.getElementById('export-mediawiki');
    const cssTextarea = document.getElementById('export-css');
    
    // 生成代码
    const mediawikiCode = generateMediaWikiCode();
    const cssCode = generateCSSCode();
    
    mediawikiTextarea.value = mediawikiCode;
    cssTextarea.value = cssCode;
    
    modal.style.display = 'block';
}

// 清空画布
function clearCanvas(skipConfirm = false) {
    if (skipConfirm || confirm('确定要清空所有内容吗？此操作不可撤销。')) {
        designData.elements = [];
        selectedElement = null;

        // 清空画布
        const elements = designCanvas.querySelectorAll('.design-element');
        elements.forEach(el => el.remove());

        // 显示占位符
        const placeholder = designCanvas.querySelector('.canvas-placeholder');
        if (placeholder) {
            placeholder.style.display = 'block';
        }

        updatePropertiesPanel();
        updateCode();

        return true;
    }
    return false;
}

// 复制代码到剪贴板
function copyCode(textareaId) {
    const textarea = document.getElementById(textareaId);
    textarea.select();
    document.execCommand('copy');
    
    // 显示复制成功提示
    const btn = textarea.nextElementSibling;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-check"></i> 已复制';
    btn.style.backgroundColor = '#10b981';
    
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.style.backgroundColor = '';
    }, 2000);
}

// 更新代码显示
function updateCode() {
    const mediawikiCode = generateMediaWikiCode();
    const cssCode = generateCSSCode();

    const mediawikiTextarea = document.getElementById('mediawiki-code');
    const cssTextarea = document.getElementById('css-code');

    if (mediawikiTextarea) mediawikiTextarea.value = mediawikiCode;
    if (cssTextarea) cssTextarea.value = cssCode;
}

// 初始化应用
function initializeApp() {
    console.log('Initializing MediaWiki Template Designer...');

    initializeDragAndDrop();
    initializeTabs();
    initializeEventListeners();
    initializeModals();

    // 显示占位符
    const placeholder = designCanvas.querySelector('.canvas-placeholder');
    if (placeholder) {
        placeholder.style.display = 'block';
    }

    // 测试属性面板功能
    testPropertyPanelFunctionality();

    console.log('Application initialized successfully');
}

// 测试属性面板功能
function testPropertyPanelFunctionality() {
    console.log('Testing property panel functionality...');

    // 检查必要的DOM元素是否存在
    const requiredElements = [
        'no-selection',
        'element-properties'
    ];

    const missingElements = requiredElements.filter(id => !document.getElementById(id));

    if (missingElements.length > 0) {
        console.error('Missing required DOM elements for property panel:', missingElements);
        return false;
    }

    console.log('Property panel DOM elements found successfully');
    return true;
}

// AI 配置管理
let aiConfig = {
    provider: 'openai',
    apiEndpoint: 'https://api.openai.com/v1/chat/completions',
    apiKey: '',
    model: 'gpt-3.5-turbo',
    temperature: 0.7,
    visionEnabled: true,
    imageMaxSize: 5
};

// 加载AI配置
function loadAIConfig() {
    const saved = localStorage.getItem('aiConfig');
    if (saved) {
        aiConfig = { ...aiConfig, ...JSON.parse(saved) };
    }
    updateAIConfigUI();
}

// 保存AI配置
function saveAIConfig() {
    localStorage.setItem('aiConfig', JSON.stringify(aiConfig));
    console.log('AI配置已保存');
}

// 更新AI配置UI
function updateAIConfigUI() {
    document.getElementById('aiProvider').value = aiConfig.provider;
    document.getElementById('aiApiEndpoint').value = aiConfig.apiEndpoint;
    document.getElementById('aiApiKey').value = aiConfig.apiKey;
    document.getElementById('aiModel').value = aiConfig.model;
    document.getElementById('aiTemperature').value = aiConfig.temperature;
    document.getElementById('temperatureValue').textContent = aiConfig.temperature;
    document.getElementById('aiVisionEnabled').checked = aiConfig.visionEnabled;
    document.getElementById('aiImageMaxSize').value = aiConfig.imageMaxSize;
}

// AI 助手功能
function showAIAssistant() {
    const modal = document.getElementById('aiAssistantModal');
    modal.style.display = 'block';
}

// 显示AI配置
function showAIConfig() {
    const modal = document.getElementById('aiConfigModal');
    modal.style.display = 'block';
    updateAIConfigUI();
}

function sendAIMessage() {
    const input = document.getElementById('aiInput');
    const message = input.value.trim();
    const imagePreview = document.getElementById('aiImagePreview');
    const hasImage = imagePreview.style.display !== 'none';

    if (!message && !hasImage) return;

    // 检查API配置
    if (!aiConfig.apiKey) {
        addAIMessage('请先在AI配置中设置API密钥。', 'assistant');
        return;
    }

    // 添加用户消息
    let userMessage = message;
    if (hasImage) {
        userMessage += ' [包含图片]';
    }
    addAIMessage(userMessage, 'user');

    // 清空输入框和图片
    input.value = '';
    clearImagePreview();

    // 处理AI响应
    if (hasImage) {
        processAIRequestWithImage(message);
    } else {
        processAIRequest(message);
    }
}

function addAIMessage(content, sender) {
    const messagesContainer = document.getElementById('aiMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `ai-message ai-message-${sender}`;

    const avatar = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

    messageDiv.innerHTML = `
        <div class="ai-avatar">${avatar}</div>
        <div class="ai-content">${content}</div>
    `;

    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// 图片处理功能
function handleImageUpload() {
    const fileInput = document.getElementById('aiImageUpload');
    fileInput.click();
}

function processImageUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    // 检查文件大小
    const maxSize = aiConfig.imageMaxSize * 1024 * 1024; // MB to bytes
    if (file.size > maxSize) {
        alert(`图片大小不能超过 ${aiConfig.imageMaxSize}MB`);
        return;
    }

    // 检查文件类型
    if (!file.type.startsWith('image/')) {
        alert('请选择有效的图片文件');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        showImagePreview(e.target.result);
    };
    reader.readAsDataURL(file);
}

function showImagePreview(imageSrc) {
    const preview = document.getElementById('aiImagePreview');
    const img = document.getElementById('aiPreviewImg');

    img.src = imageSrc;
    preview.style.display = 'flex';
}

function clearImagePreview() {
    const preview = document.getElementById('aiImagePreview');
    const img = document.getElementById('aiPreviewImg');
    const fileInput = document.getElementById('aiImageUpload');

    img.src = '';
    preview.style.display = 'none';
    fileInput.value = '';
}

async function processAIRequest(message) {
    // 显示思考中的消息
    addAIMessage('正在分析您的需求...', 'assistant');

    try {
        if (aiConfig.apiKey) {
            // 使用真实API
            const response = await callAIAPI(message);

            // 移除思考中的消息
            const messages = document.getElementById('aiMessages');
            messages.removeChild(messages.lastChild);

            addAIMessage(response, 'assistant');

            // 尝试解析响应中的模板指令
            const templateConfig = parseAIResponse(response);
            if (templateConfig) {
                generateAITemplate(templateConfig);
            }
        } else {
            // 使用本地分析（原有逻辑）
            setTimeout(() => {
                const messages = document.getElementById('aiMessages');
                messages.removeChild(messages.lastChild);

                const templateConfig = analyzeUserRequest(message);

                if (templateConfig) {
                    generateAITemplate(templateConfig);
                    addAIMessage(`我已经为您创建了${templateConfig.description}。您可以在设计区域中看到生成的模板，并可以进一步编辑和自定义。`, 'assistant');
                } else {
                    addAIMessage('抱歉，我没有完全理解您的需求。请尝试更具体地描述您想要创建的模板类型，例如："创建一个人物信息框"或"制作一个导航模板"。', 'assistant');
                }
            }, 1500);
        }
    } catch (error) {
        // 移除思考中的消息
        const messages = document.getElementById('aiMessages');
        if (messages.lastChild) {
            messages.removeChild(messages.lastChild);
        }

        addAIMessage(`抱歉，AI服务出现错误：${error.message}。请检查您的API配置或稍后重试。`, 'assistant');
    }
}

async function processAIRequestWithImage(message) {
    if (!aiConfig.visionEnabled) {
        addAIMessage('图片分析功能未启用，请在AI配置中启用。', 'assistant');
        return;
    }

    addAIMessage('正在分析图片和您的需求...', 'assistant');

    try {
        const imageData = document.getElementById('aiPreviewImg').src;
        const response = await callAIAPIWithImage(message, imageData);

        // 移除思考中的消息
        const messages = document.getElementById('aiMessages');
        messages.removeChild(messages.lastChild);

        addAIMessage(response, 'assistant');

        // 尝试解析响应中的模板指令
        const templateConfig = parseAIResponse(response);
        if (templateConfig) {
            generateAITemplate(templateConfig);
        }
    } catch (error) {
        const messages = document.getElementById('aiMessages');
        if (messages.lastChild) {
            messages.removeChild(messages.lastChild);
        }

        addAIMessage(`图片分析失败：${error.message}。请检查您的API配置。`, 'assistant');
    }
}

// API调用函数
async function callAIAPI(message) {
    const response = await fetch(aiConfig.apiEndpoint, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${aiConfig.apiKey}`
        },
        body: JSON.stringify({
            model: aiConfig.model,
            messages: [
                {
                    role: 'system',
                    content: '你是一个MediaWiki模板设计助手。请根据用户的需求，分析并建议合适的模板结构。如果需要创建模板，请在回复中包含 [TEMPLATE:类型] 指令，其中类型可以是 infobox-person, navigation, notice, table 等。'
                },
                {
                    role: 'user',
                    content: message
                }
            ],
            temperature: aiConfig.temperature,
            max_tokens: 1000
        })
    });

    if (!response.ok) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
}

async function callAIAPIWithImage(message, imageData) {
    const response = await fetch(aiConfig.apiEndpoint, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${aiConfig.apiKey}`
        },
        body: JSON.stringify({
            model: aiConfig.model,
            messages: [
                {
                    role: 'system',
                    content: '你是一个MediaWiki模板设计助手。请分析用户上传的图片，并根据图片内容和用户需求建议合适的模板结构。如果需要创建模板，请在回复中包含 [TEMPLATE:类型] 指令。'
                },
                {
                    role: 'user',
                    content: [
                        {
                            type: 'text',
                            text: message
                        },
                        {
                            type: 'image_url',
                            image_url: {
                                url: imageData
                            }
                        }
                    ]
                }
            ],
            temperature: aiConfig.temperature,
            max_tokens: 1000
        })
    });

    if (!response.ok) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
}

// 解析AI响应中的模板指令
function parseAIResponse(response) {
    const templateMatch = response.match(/\[TEMPLATE:([^\]]+)\]/);
    if (templateMatch) {
        const templateType = templateMatch[1];
        return {
            type: templateType,
            description: `${templateType}模板`
        };
    }
    return null;
}

function analyzeUserRequest(message) {
    const lowerMessage = message.toLowerCase();

    // 人物信息框
    if (lowerMessage.includes('人物') || lowerMessage.includes('信息框') || lowerMessage.includes('infobox')) {
        return {
            type: 'infobox-person',
            description: '人物信息框模板'
        };
    }

    // 导航模板
    if (lowerMessage.includes('导航') || lowerMessage.includes('链接') || lowerMessage.includes('菜单')) {
        return {
            type: 'navigation',
            description: '导航模板'
        };
    }

    // 提示框
    if (lowerMessage.includes('提示') || lowerMessage.includes('警告') || lowerMessage.includes('注意')) {
        return {
            type: 'notice',
            description: '提示框模板'
        };
    }

    // 表格
    if (lowerMessage.includes('表格') || lowerMessage.includes('数据')) {
        return {
            type: 'table',
            description: '数据表格模板'
        };
    }

    return null;
}

function generateAITemplate(config) {
    // 清空现有内容（跳过确认）
    clearCanvas(true);

    switch (config.type) {
        case 'infobox-person':
            generatePersonInfobox();
            break;
        case 'navigation':
            generateNavigationTemplate();
            break;
        case 'notice':
            generateNoticeTemplate();
            break;
        case 'table':
            generateTableTemplate();
            break;
    }
}

function generatePersonInfobox() {
    // 创建容器
    createElement('container', 0, 0);

    // 添加标题
    setTimeout(() => {
        createElement('heading', 0, 0);
        const headingElement = document.querySelector('.design-element:last-child');
        const headingData = designData.elements.find(el => el.id === headingElement.id);
        headingData.properties['标题内容'] = '{{{姓名|人物姓名}}}';
        headingData.properties['标题级别'] = '2';
        updateElementDOM(headingElement.id);

        // 添加图片
        createElement('image', 0, 0);
        const imageElement = document.querySelector('.design-element:last-child');
        const imageData = designData.elements.find(el => el.id === imageElement.id);
        imageData.properties['图片源'] = '{{{头像|Example.jpg}}}';
        imageData.properties['替代文本'] = '{{{姓名|人物姓名}}}的照片';
        imageData.properties['宽度'] = '200';
        updateElementDOM(imageElement.id);

        // 添加基本信息
        createElement('text', 0, 0);
        const textElement = document.querySelector('.design-element:last-child');
        const textData = designData.elements.find(el => el.id === textElement.id);
        textData.properties['内容'] = `
<strong>出生日期：</strong>{{{出生日期|未知}}}<br>
<strong>职业：</strong>{{{职业|未知}}}<br>
<strong>国籍：</strong>{{{国籍|未知}}}
        `.trim();
        updateElementDOM(textElement.id);

        updateCode();
    }, 100);
}

function generateNavigationTemplate() {
    // 创建导航容器
    createElement('container', 0, 0);

    setTimeout(() => {
        // 添加导航标题
        createElement('heading', 0, 0);
        const headingElement = document.querySelector('.design-element:last-child');
        const headingData = designData.elements.find(el => el.id === headingElement.id);
        headingData.properties['标题内容'] = '{{{标题|导航菜单}}}';
        headingData.properties['标题级别'] = '3';
        updateElementDOM(headingElement.id);

        // 添加链接列表
        createElement('list', 0, 0);
        const listElement = document.querySelector('.design-element:last-child');
        const listData = designData.elements.find(el => el.id === listElement.id);
        listData.properties['列表项'] = `[[{{{链接1|首页}}}|{{{链接1文本|首页}}}]]
[[{{{链接2|关于}}}|{{{链接2文本|关于我们}}}]]
[[{{{链接3|联系}}}|{{{链接3文本|联系我们}}}]]
[[{{{链接4|帮助}}}|{{{链接4文本|帮助中心}}}]]
[[{{{链接5|更多}}}|{{{链接5文本|更多信息}}}]]`;
        updateElementDOM(listElement.id);

        updateCode();
    }, 100);
}

function generateNoticeTemplate() {
    createElement('notice', 0, 0);

    setTimeout(() => {
        const noticeElement = document.querySelector('.design-element:last-child');
        const noticeData = designData.elements.find(el => el.id === noticeElement.id);
        noticeData.properties['提示类型'] = '{{{类型|info}}}';
        noticeData.properties['标题'] = '{{{标题|重要提示}}}';
        noticeData.properties['内容'] = '{{{内容|这是一个重要的提示信息，请仔细阅读。}}}';
        updateElementDOM(noticeElement.id);

        updateCode();
    }, 100);
}

function generateTableTemplate() {
    createElement('table', 0, 0);

    setTimeout(() => {
        const tableElement = document.querySelector('.design-element:last-child');
        const tableData = designData.elements.find(el => el.id === tableElement.id);
        tableData.properties['行数'] = '4';
        tableData.properties['列数'] = '3';
        updateElementDOM(tableElement.id);

        updateCode();
    }, 100);
}

// 示例模板加载功能
function loadExample(exampleType) {
    if (confirm('加载示例模板将清空当前设计，确定要继续吗？')) {
        clearCanvas(true);

        switch (exampleType) {
            case 'infobox-person':
                generatePersonInfobox();
                break;
            case 'navigation-box':
                generateNavigationTemplate();
                break;
            case 'notice-template':
                generateNoticeTemplate();
                break;
        }
    }
}

// AI配置处理函数
function handleAIConfigSave() {
    aiConfig.provider = document.getElementById('aiProvider').value;
    aiConfig.apiEndpoint = document.getElementById('aiApiEndpoint').value;
    aiConfig.apiKey = document.getElementById('aiApiKey').value;
    aiConfig.model = document.getElementById('aiModel').value;
    aiConfig.temperature = parseFloat(document.getElementById('aiTemperature').value);
    aiConfig.visionEnabled = document.getElementById('aiVisionEnabled').checked;
    aiConfig.imageMaxSize = parseInt(document.getElementById('aiImageMaxSize').value);

    saveAIConfig();
    alert('AI配置已保存');
}

async function handleAIConfigTest() {
    if (!aiConfig.apiKey) {
        alert('请先输入API密钥');
        return;
    }

    try {
        const response = await callAIAPI('测试连接');
        alert('API连接测试成功！');
    } catch (error) {
        alert(`API连接测试失败：${error.message}`);
    }
}

function handleAIConfigReset() {
    if (confirm('确定要重置所有AI配置吗？')) {
        aiConfig = {
            provider: 'openai',
            apiEndpoint: 'https://api.openai.com/v1/chat/completions',
            apiKey: '',
            model: 'gpt-3.5-turbo',
            temperature: 0.7,
            visionEnabled: true,
            imageMaxSize: 5
        };
        updateAIConfigUI();
        saveAIConfig();
        alert('AI配置已重置');
    }
}

// 当DOM加载完成后初始化应用
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    loadAIConfig();

    // 绑定AI助手按钮事件
    document.getElementById('aiAssistantBtn').addEventListener('click', showAIAssistant);
    document.getElementById('aiConfigBtn').addEventListener('click', showAIConfig);

    // 绑定AI发送按钮事件
    document.getElementById('aiSendBtn').addEventListener('click', sendAIMessage);

    // 绑定AI输入框回车事件
    document.getElementById('aiInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendAIMessage();
        }
    });

    // 绑定图片上传事件
    document.getElementById('aiImageBtn').addEventListener('click', handleImageUpload);
    document.getElementById('aiImageUpload').addEventListener('change', processImageUpload);
    document.getElementById('aiRemoveImage').addEventListener('click', clearImagePreview);

    // 绑定AI配置事件
    document.getElementById('aiConfigSave').addEventListener('click', handleAIConfigSave);
    document.getElementById('aiConfigTest').addEventListener('click', handleAIConfigTest);
    document.getElementById('aiConfigReset').addEventListener('click', handleAIConfigReset);

    // 绑定温度滑块事件
    document.getElementById('aiTemperature').addEventListener('input', function(e) {
        document.getElementById('temperatureValue').textContent = e.target.value;
    });

    // 绑定提供商选择事件
    document.getElementById('aiProvider').addEventListener('change', function(e) {
        const provider = e.target.value;
        const endpointInput = document.getElementById('aiApiEndpoint');
        const modelInput = document.getElementById('aiModel');

        switch (provider) {
            case 'openai':
                endpointInput.value = 'https://api.openai.com/v1/chat/completions';
                modelInput.value = 'gpt-3.5-turbo';
                break;
            case 'claude':
                endpointInput.value = 'https://api.anthropic.com/v1/messages';
                modelInput.value = 'claude-3-sonnet-20240229';
                break;
            case 'custom':
                endpointInput.value = '';
                modelInput.value = '';
                break;
        }
    });
});
