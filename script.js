// 全局变量
let selectedElement = null;
let elementCounter = 0;
let designData = {
    elements: [],
    styles: {}
};

// DOM 元素
const designCanvas = document.getElementById('designCanvas');
const componentsPanel = document.querySelector('.components-panel');
const propertiesPanel = document.querySelector('.properties-panel');

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    initializeDragAndDrop();
    initializeTabs();
    initializeEventListeners();
    initializeModals();
});

// 初始化拖拽功能
function initializeDragAndDrop() {
    // 组件拖拽开始
    const componentItems = document.querySelectorAll('.component-item');
    componentItems.forEach(item => {
        item.addEventListener('dragstart', function(e) {
            e.dataTransfer.setData('text/plain', this.dataset.type);
            e.dataTransfer.setData('source', 'component');
            this.style.opacity = '0.5';
        });

        item.addEventListener('dragend', function(e) {
            this.style.opacity = '1';
        });
    });

    // 设计画布拖拽处理
    designCanvas.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('drag-over');

        // 显示插入指示器
        showDropIndicator(e);
    });

    designCanvas.addEventListener('dragleave', function(e) {
        if (!this.contains(e.relatedTarget)) {
            this.classList.remove('drag-over');
            hideDropIndicator();
        }
    });

    designCanvas.addEventListener('drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        this.classList.remove('drag-over');
        hideDropIndicator();

        const componentType = e.dataTransfer.getData('text/plain');
        const source = e.dataTransfer.getData('source');

        if (source === 'component') {
            // 获取插入位置
            const insertPosition = getInsertPosition(e);
            createElement(componentType, 0, 0, insertPosition);
        } else if (source === 'element') {
            // 移动现有元素
            const elementId = e.dataTransfer.getData('elementId');
            const insertPosition = getInsertPosition(e);
            moveElement(elementId, insertPosition);
        }
    });

    // 初始化现有元素的拖拽功能
    initializeElementDragAndDrop();
}

// 显示拖拽插入指示器
function showDropIndicator(e) {
    hideDropIndicator(); // 先隐藏之前的指示器

    const rect = designCanvas.getBoundingClientRect();
    const y = e.clientY - rect.top;

    // 找到最接近的插入位置
    const elements = Array.from(designCanvas.querySelectorAll('.design-element'));
    let insertAfter = null;

    for (let element of elements) {
        const elementRect = element.getBoundingClientRect();
        const elementY = elementRect.top - rect.top + elementRect.height / 2;

        if (y > elementY) {
            insertAfter = element;
        } else {
            break;
        }
    }

    // 创建插入指示器
    const indicator = document.createElement('div');
    indicator.className = 'drop-indicator';
    indicator.innerHTML = '<div class="drop-line"></div><span class="drop-text">在此处插入</span>';

    if (insertAfter) {
        insertAfter.parentNode.insertBefore(indicator, insertAfter.nextSibling);
    } else {
        // 插入到开头
        if (elements.length > 0) {
            designCanvas.insertBefore(indicator, elements[0]);
        } else {
            designCanvas.appendChild(indicator);
        }
    }
}

// 隐藏拖拽插入指示器
function hideDropIndicator() {
    const indicators = document.querySelectorAll('.drop-indicator');
    indicators.forEach(indicator => indicator.remove());
}

// 获取插入位置
function getInsertPosition(e) {
    const rect = designCanvas.getBoundingClientRect();
    const y = e.clientY - rect.top;

    const elements = Array.from(designCanvas.querySelectorAll('.design-element'));

    for (let i = 0; i < elements.length; i++) {
        const elementRect = elements[i].getBoundingClientRect();
        const elementY = elementRect.top - rect.top + elementRect.height / 2;

        if (y < elementY) {
            return { before: elements[i] };
        }
    }

    // 插入到末尾
    return { after: elements[elements.length - 1] || null };
}

// 初始化元素间拖拽功能
function initializeElementDragAndDrop() {
    // 这个函数会在创建新元素时被调用
}

// 创建设计元素
function createElement(type, x = 0, y = 0, insertPosition = null) {
    elementCounter++;
    const elementId = `element_${elementCounter}`;

    // 移除占位符
    const placeholder = designCanvas.querySelector('.canvas-placeholder');
    if (placeholder) {
        placeholder.style.display = 'none';
    }

    const element = document.createElement('div');
    element.className = 'design-element';
    element.id = elementId;
    element.dataset.type = type;

    // 添加控制按钮
    const controls = document.createElement('div');
    controls.className = 'element-controls';
    controls.innerHTML = `
        <button class="element-control-btn duplicate" onclick="duplicateElement('${elementId}')">
            <i class="fas fa-copy"></i>
        </button>
        <button class="element-control-btn delete" onclick="deleteElement('${elementId}')">
            <i class="fas fa-trash"></i>
        </button>
    `;
    element.appendChild(controls);

    // 根据类型创建内容
    const content = createElementContent(type, elementId);
    element.appendChild(content);

    // 添加点击事件
    element.addEventListener('click', function(e) {
        e.stopPropagation();
        selectElement(this);
    });

    // 添加元素拖拽功能
    setupElementDragging(element);

    // 插入到指定位置
    if (insertPosition) {
        if (insertPosition.before) {
            designCanvas.insertBefore(element, insertPosition.before);
        } else if (insertPosition.after) {
            designCanvas.insertBefore(element, insertPosition.after.nextSibling);
        } else {
            designCanvas.appendChild(element);
        }
    } else {
        designCanvas.appendChild(element);
    }

    // 保存到设计数据
    const elementData = {
        id: elementId,
        type: type,
        properties: getDefaultProperties(type),
        styles: getDefaultStyles(type)
    };

    designData.elements.push(elementData);

    // 自动选择新创建的元素
    selectElement(element);

    // 更新代码
    updateCode();
}

// 移动元素到新位置
function moveElement(elementId, insertPosition) {
    const element = document.getElementById(elementId);
    if (!element) return;

    // 移动DOM元素
    if (insertPosition.before) {
        designCanvas.insertBefore(element, insertPosition.before);
    } else if (insertPosition.after) {
        designCanvas.insertBefore(element, insertPosition.after.nextSibling);
    } else {
        designCanvas.appendChild(element);
    }

    // 更新代码
    updateCode();
}

// 在容器内创建元素
function createElementInContainer(type, container) {
    elementCounter++;
    const elementId = `element_${elementCounter}`;

    const element = document.createElement('div');
    element.className = 'design-element nested-element';
    element.id = elementId;
    element.dataset.type = type;

    // 添加控制按钮
    const controls = document.createElement('div');
    controls.className = 'element-controls';
    controls.innerHTML = `
        <button class="element-control-btn duplicate" onclick="duplicateElement('${elementId}')">
            <i class="fas fa-copy"></i>
        </button>
        <button class="element-control-btn delete" onclick="deleteElement('${elementId}')">
            <i class="fas fa-trash"></i>
        </button>
    `;
    element.appendChild(controls);

    // 根据类型创建内容
    const content = createElementContent(type, elementId);
    element.appendChild(content);

    // 添加点击事件
    element.addEventListener('click', function(e) {
        e.stopPropagation();
        selectElement(this);
    });

    // 添加元素拖拽功能
    setupElementDragging(element);

    // 清空容器占位符并添加元素
    if (container.querySelector('.container-placeholder, .row-placeholder, .column-placeholder')) {
        container.innerHTML = '';
    }
    container.appendChild(element);

    // 保存到设计数据
    const elementData = {
        id: elementId,
        type: type,
        properties: getDefaultProperties(type),
        styles: getDefaultStyles(type),
        parent: container.closest('.design-element')?.id || null
    };

    designData.elements.push(elementData);

    // 自动选择新创建的元素
    selectElement(element);

    // 更新代码
    updateCode();
}

// 设置元素拖拽功能
function setupElementDragging(element) {
    element.draggable = true;

    element.addEventListener('dragstart', function(e) {
        e.stopPropagation();
        e.dataTransfer.setData('text/plain', this.dataset.type);
        e.dataTransfer.setData('source', 'element');
        e.dataTransfer.setData('elementId', this.id);
        this.style.opacity = '0.5';
    });

    element.addEventListener('dragend', function(e) {
        this.style.opacity = '1';
    });
}

// 移动元素到容器
function moveElementToContainer(elementId, container) {
    const element = document.getElementById(elementId);
    if (!element) return;

    // 清空容器占位符
    if (container.querySelector('.container-placeholder, .row-placeholder, .column-placeholder')) {
        container.innerHTML = '';
    }

    // 移动元素
    container.appendChild(element);

    // 更新数据中的父级关系
    const elementData = designData.elements.find(el => el.id === elementId);
    if (elementData) {
        elementData.parent = container.closest('.design-element')?.id || null;
    }

    // 更新代码
    updateCode();
}

// 创建元素内容
function createElementContent(type, elementId) {
    const content = document.createElement('div');
    content.className = 'element-content';

    switch(type) {
        case 'text':
            content.innerHTML = '<p>这是一段文本内容，可以包含多行文字和段落格式。</p>';
            break;
        case 'heading':
            content.innerHTML = '<h2>这是标题</h2>';
            break;
        case 'image':
            content.innerHTML = '<div class="image-placeholder"><i class="fas fa-image"></i><br>图片占位符<br><small>点击编辑设置图片源</small></div>';
            break;
        case 'link':
            content.innerHTML = '<a href="#">这是一个链接</a>';
            break;
        case 'list':
            content.innerHTML = `
                <ul>
                    <li>列表项目 1</li>
                    <li>列表项目 2</li>
                    <li>列表项目 3</li>
                </ul>
            `;
            break;
        case 'quote':
            content.innerHTML = '<blockquote>这是一段引用文本，可以用来引用其他来源的内容。</blockquote>';
            break;
        case 'container':
            content.innerHTML = '<div class="container-placeholder">容器 - 拖拽其他元素到这里</div>';
            content.classList.add('drop-zone');
            setupContainerDropZone(content);
            break;
        case 'row':
            content.innerHTML = '<div class="row-placeholder">行布局 - 水平排列元素</div>';
            content.classList.add('drop-zone');
            setupContainerDropZone(content);
            break;
        case 'column':
            content.innerHTML = '<div class="column-placeholder">列布局 - 垂直排列元素</div>';
            content.classList.add('drop-zone');
            setupContainerDropZone(content);
            break;
        case 'table':
            content.innerHTML = `
                <table border="1" class="wikitable">
                    <tr><th>标题1</th><th>标题2</th><th>标题3</th></tr>
                    <tr><td>数据1</td><td>数据2</td><td>数据3</td></tr>
                    <tr><td>数据4</td><td>数据5</td><td>数据6</td></tr>
                </table>
            `;
            break;
        case 'tabs':
            content.innerHTML = `
                <div class="tabs-container">
                    <div class="tab-headers">
                        <div class="tab-header active">标签1</div>
                        <div class="tab-header">标签2</div>
                        <div class="tab-header">标签3</div>
                    </div>
                    <div class="tab-content">
                        <div class="tab-pane active">标签1的内容</div>
                        <div class="tab-pane">标签2的内容</div>
                        <div class="tab-pane">标签3的内容</div>
                    </div>
                </div>
            `;
            break;
        case 'collapsible':
            content.innerHTML = `
                <div class="collapsible">
                    <div class="collapsible-header">
                        <i class="fas fa-chevron-down"></i> 点击展开/折叠
                    </div>
                    <div class="collapsible-content">
                        这里是可折叠的内容区域，可以包含任何内容。
                    </div>
                </div>
            `;
            break;
        case 'infobox':
            content.innerHTML = `
                <div class="infobox">
                    <div class="infobox-title">信息框标题</div>
                    <div class="infobox-image">
                        <div class="image-placeholder"><i class="fas fa-image"></i></div>
                    </div>
                    <div class="infobox-content">
                        <div class="infobox-row">
                            <span class="label">姓名:</span>
                            <span class="value">示例名称</span>
                        </div>
                        <div class="infobox-row">
                            <span class="label">类型:</span>
                            <span class="value">示例类型</span>
                        </div>
                        <div class="infobox-row">
                            <span class="label">日期:</span>
                            <span class="value">2024年</span>
                        </div>
                    </div>
                </div>
            `;
            break;
        case 'navbox':
            content.innerHTML = `
                <div class="navbox">
                    <div class="navbox-title">导航框标题</div>
                    <div class="navbox-content">
                        <div class="navbox-group">
                            <div class="navbox-group-title">分组1</div>
                            <div class="navbox-links">
                                <a href="#">链接1</a> • <a href="#">链接2</a> • <a href="#">链接3</a>
                            </div>
                        </div>
                        <div class="navbox-group">
                            <div class="navbox-group-title">分组2</div>
                            <div class="navbox-links">
                                <a href="#">链接4</a> • <a href="#">链接5</a> • <a href="#">链接6</a>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            break;
        case 'template-param':
            content.innerHTML = '<span class="template-param">{{{参数名|默认值}}}</span>';
            break;
        case 'category':
            content.innerHTML = '<div class="category-tag">[[Category:示例分类]]</div>';
            break;
        case 'gallery':
            content.innerHTML = `
                <div class="gallery">
                    <div class="gallery-title">图片画廊</div>
                    <div class="gallery-grid">
                        <div class="gallery-item">
                            <div class="image-placeholder"><i class="fas fa-image"></i></div>
                            <div class="gallery-caption">图片1说明</div>
                        </div>
                        <div class="gallery-item">
                            <div class="image-placeholder"><i class="fas fa-image"></i></div>
                            <div class="gallery-caption">图片2说明</div>
                        </div>
                        <div class="gallery-item">
                            <div class="image-placeholder"><i class="fas fa-image"></i></div>
                            <div class="gallery-caption">图片3说明</div>
                        </div>
                    </div>
                </div>
            `;
            break;
        case 'notice':
            content.innerHTML = `
                <div class="notice notice-info">
                    <div class="notice-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="notice-content">
                        <div class="notice-title">提示</div>
                        <div class="notice-text">这是一个提示消息框，可以用来显示重要信息。</div>
                    </div>
                </div>
            `;
            break;
        default:
            content.innerHTML = '<div>未知组件类型</div>';
    }

    return content;
}

// 设置容器拖拽区域
function setupContainerDropZone(container) {
    container.addEventListener('dragover', function(e) {
        e.preventDefault();
        e.stopPropagation();
        this.classList.add('drag-over');
    });

    container.addEventListener('dragleave', function(e) {
        e.stopPropagation();
        if (!this.contains(e.relatedTarget)) {
            this.classList.remove('drag-over');
        }
    });

    container.addEventListener('drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        this.classList.remove('drag-over');

        const componentType = e.dataTransfer.getData('text/plain');
        const source = e.dataTransfer.getData('source');

        if (source === 'component') {
            // 在容器内创建新元素
            createElementInContainer(componentType, this);
        } else if (source === 'element') {
            // 移动现有元素到容器
            const elementId = e.dataTransfer.getData('elementId');
            moveElementToContainer(elementId, this);
        }
    });
}

// 获取默认属性
function getDefaultProperties(type) {
    const defaults = {
        text: {
            '内容': '这是一段文本内容，可以包含多行文字和段落格式。',
            '标签类型': 'p',
            '对齐方式': 'left'
        },
        heading: {
            '标题内容': '这是标题',
            '标题级别': '2',
            '对齐方式': 'left'
        },
        image: {
            '图片源': '',
            '替代文本': '图片描述',
            '宽度': '',
            '高度': '',
            '显示方式': 'thumb'
        },
        link: {
            '链接地址': '#',
            '显示文本': '这是一个链接',
            '打开方式': '_self',
            '链接类型': 'internal'
        },
        list: {
            '列表类型': 'ul',
            '列表项': '列表项目 1\n列表项目 2\n列表项目 3'
        },
        quote: {
            '引用内容': '这是一段引用文本，可以用来引用其他来源的内容。',
            '引用来源': '',
            '引用类型': 'blockquote'
        },
        container: {
            '容器类名': 'container',
            '容器标题': ''
        },
        row: {
            '行类名': 'row',
            '对齐方式': 'left'
        },
        column: {
            '列类名': 'column',
            '列宽度': '12',
            '响应式': 'true'
        },
        table: {
            '行数': '3',
            '列数': '3',
            '表格样式': 'wikitable',
            '显示边框': 'true'
        },
        tabs: {
            '标签页数量': '3',
            '默认激活': '1',
            '标签标题': '标签1,标签2,标签3'
        },
        collapsible: {
            '标题': '点击展开/折叠',
            '默认状态': 'collapsed',
            '动画效果': 'true'
        },
        infobox: {
            '信息框标题': '信息框标题',
            '信息框类名': 'infobox',
            '宽度': '300px',
            '位置': 'right'
        },
        navbox: {
            '导航框标题': '导航框标题',
            '导航框类名': 'navbox',
            '折叠状态': 'expanded'
        },
        'template-param': {
            '参数名称': '参数名',
            '默认值': '默认值',
            '参数描述': ''
        },
        category: {
            '分类名称': '示例分类',
            '排序键': ''
        },
        gallery: {
            '画廊标题': '图片画廊',
            '每行图片数': '3',
            '图片大小': 'medium'
        },
        notice: {
            '提示类型': 'info',
            '标题': '提示',
            '内容': '这是一个提示消息框，可以用来显示重要信息。',
            '显示图标': 'true'
        }
    };

    return defaults[type] || {};
}

// 获取默认样式
function getDefaultStyles(type) {
    const defaults = {
        text: { 'font-size': '14px', 'color': '#000000' },
        heading: { 'font-size': '24px', 'font-weight': 'bold', 'color': '#000000' },
        image: { 'max-width': '100%', 'height': 'auto' },
        link: { 'color': '#0066cc', 'text-decoration': 'underline' },
        container: { 'padding': '10px', 'border': '1px solid #ccc' },
        row: { 'display': 'flex', 'flex-wrap': 'wrap' },
        column: { 'flex': '1', 'padding': '5px' },
        table: { 'border-collapse': 'collapse', 'width': '100%' },
        infobox: { 'border': '1px solid #aaa', 'background': '#f9f9f9', 'padding': '10px', 'width': '300px' },
        navbox: { 'border': '1px solid #aaa', 'background': '#f0f0f0', 'padding': '5px' },
        'template-param': { 'background': '#ffffcc', 'padding': '2px 4px', 'border-radius': '3px' }
    };
    
    return defaults[type] || {};
}

// 选择元素
function selectElement(element) {
    // 移除之前的选择
    const previousSelected = document.querySelector('.design-element.selected');
    if (previousSelected) {
        previousSelected.classList.remove('selected');
    }
    
    // 选择新元素
    element.classList.add('selected');
    selectedElement = element;
    
    // 更新属性面板
    updatePropertiesPanel();
    updateStylesPanel();
}

// 更新属性面板
function updatePropertiesPanel() {
    const noSelection = document.getElementById('no-selection');
    const elementProperties = document.getElementById('element-properties');

    if (!selectedElement) {
        noSelection.style.display = 'block';
        elementProperties.style.display = 'none';
        return;
    }

    noSelection.style.display = 'none';
    elementProperties.style.display = 'block';

    const elementData = designData.elements.find(el => el.id === selectedElement.id);
    if (!elementData) return;

    // 获取组件类型的中文名称
    const typeNames = {
        'text': '文本段落',
        'heading': '标题',
        'image': '图片',
        'link': '链接',
        'list': '列表',
        'quote': '引用',
        'container': '容器',
        'row': '行布局',
        'column': '列布局',
        'table': '表格',
        'tabs': '标签页',
        'collapsible': '折叠框',
        'infobox': '信息框',
        'navbox': '导航框',
        'template-param': '模板参数',
        'category': '分类标签',
        'gallery': '图片画廊',
        'notice': '提示框'
    };

    // 生成属性表单
    let propertiesHTML = `
        <div class="property-header">
            <h4><i class="fas fa-cog"></i> ${typeNames[elementData.type] || elementData.type}</h4>
            <small>编辑元素属性</small>
        </div>
    `;

    for (const [key, value] of Object.entries(elementData.properties)) {
        const inputType = getInputType(key, value);
        const inputElement = createPropertyInput(key, value, inputType, selectedElement.id);

        propertiesHTML += `
            <div class="property-row">
                <label class="property-label">
                    ${key}
                    ${getPropertyHelp(key)}
                </label>
                ${inputElement}
            </div>
        `;
    }

    elementProperties.innerHTML = propertiesHTML;
}

// 获取输入框类型
function getInputType(key, value) {
    if (key.includes('颜色') || key.includes('color')) return 'color';
    if (key.includes('数量') || key.includes('级别') || key.includes('宽度') || key.includes('行数') || key.includes('列数')) return 'number';
    if (key.includes('内容') || key.includes('项') || value.includes('\n')) return 'textarea';
    if (key.includes('类型') || key.includes('方式') || key.includes('状态')) return 'select';
    return 'text';
}

// 创建属性输入元素
function createPropertyInput(key, value, type, elementId) {
    // 确保值被正确转义以防止HTML注入
    const escapedValue = String(value).replace(/"/g, '&quot;').replace(/'/g, '&#39;');

    switch (type) {
        case 'textarea':
            return `<textarea data-property="${key}" oninput="updateElementProperty('${elementId}', '${key}', this.value)" onchange="updateElementProperty('${elementId}', '${key}', this.value)" rows="3">${value}</textarea>`;
        case 'number':
            return `<input type="number" data-property="${key}" value="${escapedValue}" oninput="updateElementProperty('${elementId}', '${key}', this.value)" onchange="updateElementProperty('${elementId}', '${key}', this.value)">`;
        case 'color':
            return `<input type="color" data-property="${key}" value="${escapedValue}" oninput="updateElementProperty('${elementId}', '${key}', this.value)" onchange="updateElementProperty('${elementId}', '${key}', this.value)">`;
        case 'select':
            return createSelectInput(key, value, elementId);
        default:
            return `<input type="text" data-property="${key}" value="${escapedValue}" oninput="updateElementProperty('${elementId}', '${key}', this.value)" onchange="updateElementProperty('${elementId}', '${key}', this.value)">`;
    }
}

// 创建选择框输入
function createSelectInput(key, value, elementId) {
    const options = getSelectOptions(key);
    let selectHTML = `<select data-property="${key}" onchange="updateElementProperty('${elementId}', '${key}', this.value)">`;

    options.forEach(option => {
        const selected = option.value === value ? 'selected' : '';
        selectHTML += `<option value="${option.value}" ${selected}>${option.label}</option>`;
    });

    selectHTML += '</select>';
    return selectHTML;
}

// 获取选择框选项
function getSelectOptions(key) {
    const optionsMap = {
        '标签类型': [
            { value: 'p', label: '段落 (p)' },
            { value: 'div', label: '区块 (div)' },
            { value: 'span', label: '行内 (span)' }
        ],
        '标题级别': [
            { value: '1', label: 'H1 - 最大标题' },
            { value: '2', label: 'H2 - 二级标题' },
            { value: '3', label: 'H3 - 三级标题' },
            { value: '4', label: 'H4 - 四级标题' },
            { value: '5', label: 'H5 - 五级标题' },
            { value: '6', label: 'H6 - 六级标题' }
        ],
        '对齐方式': [
            { value: 'left', label: '左对齐' },
            { value: 'center', label: '居中' },
            { value: 'right', label: '右对齐' },
            { value: 'justify', label: '两端对齐' }
        ],
        '显示方式': [
            { value: 'thumb', label: '缩略图' },
            { value: 'frame', label: '带框' },
            { value: 'frameless', label: '无框' },
            { value: 'border', label: '边框' }
        ],
        '打开方式': [
            { value: '_self', label: '当前窗口' },
            { value: '_blank', label: '新窗口' }
        ],
        '链接类型': [
            { value: 'internal', label: '内部链接' },
            { value: 'external', label: '外部链接' }
        ],
        '列表类型': [
            { value: 'ul', label: '无序列表' },
            { value: 'ol', label: '有序列表' }
        ],
        '引用类型': [
            { value: 'blockquote', label: '块引用' },
            { value: 'code', label: '代码块' }
        ],
        '提示类型': [
            { value: 'info', label: '信息' },
            { value: 'warning', label: '警告' },
            { value: 'error', label: '错误' },
            { value: 'success', label: '成功' }
        ],
        '默认状态': [
            { value: 'collapsed', label: '折叠' },
            { value: 'expanded', label: '展开' }
        ],
        '位置': [
            { value: 'right', label: '右侧' },
            { value: 'left', label: '左侧' },
            { value: 'center', label: '居中' }
        ]
    };

    return optionsMap[key] || [{ value: '', label: '请选择' }];
}

// 获取属性帮助信息
function getPropertyHelp(key) {
    const helpMap = {
        '内容': '<small>输入文本内容</small>',
        '标题内容': '<small>输入标题文字</small>',
        '图片源': '<small>输入图片文件名，如：Example.jpg</small>',
        '链接地址': '<small>内部链接用页面名，外部链接用完整URL</small>',
        '参数名称': '<small>模板参数的名称</small>',
        '分类名称': '<small>页面所属的分类</small>',
        '列表项': '<small>每行一个列表项</small>'
    };

    return helpMap[key] || '';
}

// 更新样式面板
function updateStylesPanel() {
    if (!selectedElement) return;
    
    const elementData = designData.elements.find(el => el.id === selectedElement.id);
    if (!elementData) return;
    
    // 填充样式输入框
    document.getElementById('style-width').value = elementData.styles.width || '';
    document.getElementById('style-height').value = elementData.styles.height || '';
    document.getElementById('style-margin').value = elementData.styles.margin || '';
    document.getElementById('style-padding').value = elementData.styles.padding || '';
    document.getElementById('style-font-size').value = elementData.styles['font-size'] || '';
    document.getElementById('style-color').value = elementData.styles.color || '#000000';
    document.getElementById('style-background').value = elementData.styles.background || '#ffffff';
    document.getElementById('style-border').value = elementData.styles.border || '';
    document.getElementById('style-border-radius').value = elementData.styles['border-radius'] || '';
}

// 更新元素属性
function updateElementProperty(elementId, property, value) {
    console.log('Updating property:', elementId, property, value); // Debug log

    const elementData = designData.elements.find(el => el.id === elementId);
    if (elementData) {
        elementData.properties[property] = value;

        // 更新DOM元素
        updateElementDOM(elementId);
        updateCode();

        console.log('Property updated successfully'); // Debug log
    } else {
        console.error('Element data not found for ID:', elementId); // Debug log
    }
}

// 更新元素样式
function updateElementStyle(elementId, property, value) {
    const elementData = designData.elements.find(el => el.id === elementId);
    if (elementData) {
        elementData.styles[property] = value;
        
        // 应用样式到DOM元素
        const element = document.getElementById(elementId);
        if (element) {
            element.style[property] = value;
        }
        
        updateCode();
    }
}

// 更新DOM元素内容
function updateElementDOM(elementId) {
    console.log('Updating DOM for element:', elementId); // Debug log

    const element = document.getElementById(elementId);
    const elementData = designData.elements.find(el => el.id === elementId);

    if (!element || !elementData) {
        console.error('Element or data not found:', elementId); // Debug log
        return;
    }

    const content = element.querySelector('.element-content');
    if (!content) {
        console.error('Element content not found for:', elementId); // Debug log
        return;
    }

    console.log('Updating content for type:', elementData.type, 'with properties:', elementData.properties); // Debug log

    // 根据类型和属性更新内容
    switch(elementData.type) {
        case 'text':
            const textContent = elementData.properties['内容'] || elementData.properties.content || '文本内容';
            const textTag = elementData.properties['标签类型'] || elementData.properties.tag || 'p';
            content.innerHTML = `<${textTag}>${textContent}</${textTag}>`;
            break;
        case 'heading':
            const headingContent = elementData.properties['标题内容'] || elementData.properties.content || '标题';
            const headingLevel = elementData.properties['标题级别'] || elementData.properties.level || '2';
            content.innerHTML = `<h${headingLevel}>${headingContent}</h${headingLevel}>`;
            break;
        case 'link':
            const linkHref = elementData.properties['链接地址'] || elementData.properties.href || '#';
            const linkText = elementData.properties['显示文本'] || elementData.properties.text || '链接';
            const linkTarget = elementData.properties['打开方式'] || elementData.properties.target || '_self';
            content.innerHTML = `<a href="${linkHref}" target="${linkTarget}">${linkText}</a>`;
            break;
        case 'image':
            const imgSrc = elementData.properties['图片源'] || elementData.properties.src || '';
            const imgAlt = elementData.properties['替代文本'] || elementData.properties.alt || '图片描述';
            const imgWidth = elementData.properties['宽度'] || elementData.properties.width || '';
            const imgHeight = elementData.properties['高度'] || elementData.properties.height || '';

            if (imgSrc) {
                const imgAttrs = [`src="${imgSrc}"`, `alt="${imgAlt}"`];
                if (imgWidth) imgAttrs.push(`width="${imgWidth}"`);
                if (imgHeight) imgAttrs.push(`height="${imgHeight}"`);
                content.innerHTML = `<img ${imgAttrs.join(' ')}>`;
            } else {
                content.innerHTML = '<div class="image-placeholder"><i class="fas fa-image"></i><br>图片占位符<br><small>点击编辑设置图片源</small></div>';
            }
            break;
        case 'list':
            const listType = elementData.properties['列表类型'] || elementData.properties.listType || 'ul';
            const listItems = (elementData.properties['列表项'] || elementData.properties.items || '列表项目 1\n列表项目 2\n列表项目 3').split('\n');
            let listHTML = `<${listType}>`;
            listItems.forEach(item => {
                if (item.trim()) {
                    listHTML += `<li>${item.trim()}</li>`;
                }
            });
            listHTML += `</${listType}>`;
            content.innerHTML = listHTML;
            break;
        case 'quote':
            const quoteContent = elementData.properties['引用内容'] || elementData.properties.content || '引用文本';
            const quoteSource = elementData.properties['引用来源'] || elementData.properties.source || '';
            let quoteHTML = `<blockquote>${quoteContent}`;
            if (quoteSource) {
                quoteHTML += `<cite>—— ${quoteSource}</cite>`;
            }
            quoteHTML += '</blockquote>';
            content.innerHTML = quoteHTML;
            break;
        case 'template-param':
            const paramName = elementData.properties['参数名称'] || elementData.properties.name || '参数名';
            const paramDefault = elementData.properties['默认值'] || elementData.properties.default || '默认值';
            content.innerHTML = `<span class="template-param">{{{${paramName}|${paramDefault}}}}</span>`;
            break;
        case 'category':
            const categoryName = elementData.properties['分类名称'] || elementData.properties.name || '示例分类';
            content.innerHTML = `<div class="category-tag">[[Category:${categoryName}]]</div>`;
            break;
        case 'notice':
            const noticeType = elementData.properties['提示类型'] || elementData.properties.type || 'info';
            const noticeTitle = elementData.properties['标题'] || elementData.properties.title || '提示';
            const noticeContent = elementData.properties['内容'] || elementData.properties.content || '提示内容';
            content.innerHTML = `
                <div class="notice notice-${noticeType}">
                    <div class="notice-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="notice-content">
                        <div class="notice-title">${noticeTitle}</div>
                        <div class="notice-text">${noticeContent}</div>
                    </div>
                </div>
            `;
            break;
        default:
            console.log('No specific update handler for type:', elementData.type); // Debug log
            break;
    }

    console.log('DOM update completed for:', elementId); // Debug log
}

// 复制元素
function duplicateElement(elementId) {
    const originalElement = document.getElementById(elementId);
    const originalData = designData.elements.find(el => el.id === elementId);

    if (!originalElement || !originalData) return;

    // 创建新元素
    elementCounter++;
    const newElementId = `element_${elementCounter}`;

    const newElement = originalElement.cloneNode(true);
    newElement.id = newElementId;

    // 更新控制按钮
    const controls = newElement.querySelector('.element-controls');
    controls.innerHTML = `
        <button class="element-control-btn duplicate" onclick="duplicateElement('${newElementId}')">
            <i class="fas fa-copy"></i>
        </button>
        <button class="element-control-btn delete" onclick="deleteElement('${newElementId}')">
            <i class="fas fa-trash"></i>
        </button>
    `;

    // 添加点击事件
    newElement.addEventListener('click', function(e) {
        e.stopPropagation();
        selectElement(this);
    });

    // 重新设置拖拽功能
    setupElementDragging(newElement);

    // 如果是容器类型，重新设置拖拽区域
    const content = newElement.querySelector('.element-content');
    if (content && (originalData.type === 'container' || originalData.type === 'row' || originalData.type === 'column')) {
        setupContainerDropZone(content);
    }

    // 插入到原元素后面
    originalElement.parentNode.insertBefore(newElement, originalElement.nextSibling);

    // 复制数据
    const newElementData = {
        id: newElementId,
        type: originalData.type,
        properties: { ...originalData.properties },
        styles: { ...originalData.styles },
        parent: originalData.parent
    };

    designData.elements.push(newElementData);

    // 选择新元素
    selectElement(newElement);
    updateCode();
}

// 删除元素
function deleteElement(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.remove();
        
        // 从数据中移除
        designData.elements = designData.elements.filter(el => el.id !== elementId);
        
        // 如果删除的是选中元素，清除选择
        if (selectedElement && selectedElement.id === elementId) {
            selectedElement = null;
            updatePropertiesPanel();
        }
        
        // 如果没有元素了，显示占位符
        if (designData.elements.length === 0) {
            const placeholder = designCanvas.querySelector('.canvas-placeholder');
            if (placeholder) {
                placeholder.style.display = 'block';
            }
        }
        
        updateCode();
    }
}

// 初始化标签页
function initializeTabs() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabPanes = document.querySelectorAll('.tab-pane');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            
            // 移除所有活动状态
            tabBtns.forEach(b => b.classList.remove('active'));
            tabPanes.forEach(p => p.classList.remove('active'));
            
            // 激活当前标签
            this.classList.add('active');
            document.getElementById(targetTab + '-tab').classList.add('active');
        });
    });
}

// 初始化事件监听器
function initializeEventListeners() {
    // 样式输入框事件
    const styleInputs = [
        'style-width', 'style-height', 'style-margin', 'style-padding',
        'style-font-size', 'style-color', 'style-background',
        'style-border', 'style-border-radius'
    ];

    styleInputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (input) {
            input.addEventListener('input', function() {
                if (selectedElement) {
                    const property = inputId.replace('style-', '');
                    updateElementStyle(selectedElement.id, property, this.value);
                }
            });
        }
    });

    // 画布点击事件（取消选择）
    designCanvas.addEventListener('click', function(e) {
        if (e.target === this) {
            selectedElement = null;
            const previousSelected = document.querySelector('.design-element.selected');
            if (previousSelected) {
                previousSelected.classList.remove('selected');
            }
            updatePropertiesPanel();
        }
    });

    // 工具栏按钮事件
    document.getElementById('previewBtn').addEventListener('click', showPreview);
    document.getElementById('exportBtn').addEventListener('click', showExport);
    document.getElementById('clearBtn').addEventListener('click', clearCanvas);

    // 添加拖拽提示
    addDragHints();
}

// 添加拖拽提示
function addDragHints() {
    // 为组件添加提示
    const componentItems = document.querySelectorAll('.component-item');
    componentItems.forEach(item => {
        item.title = '拖拽到画布或容器中';
    });

    // 监听第一次拖拽
    let firstDrag = true;
    designCanvas.addEventListener('dragenter', function() {
        if (firstDrag) {
            showDragHint();
            firstDrag = false;
        }
    });
}

// 显示拖拽提示
function showDragHint() {
    const hint = document.createElement('div');
    hint.className = 'drag-hint';
    hint.innerHTML = `
        <div class="hint-content">
            <i class="fas fa-info-circle"></i>
            <p><strong>拖拽提示：</strong></p>
            <ul>
                <li>拖拽组件到画布创建新元素</li>
                <li>拖拽组件到容器、行、列中嵌套元素</li>
                <li>拖拽现有元素可以移动位置</li>
                <li>点击元素可以编辑属性和样式</li>
            </ul>
            <button onclick="closeDragHint()">知道了</button>
        </div>
    `;

    document.body.appendChild(hint);

    // 3秒后自动关闭
    setTimeout(() => {
        if (hint.parentNode) {
            hint.remove();
        }
    }, 8000);
}

// 关闭拖拽提示
function closeDragHint() {
    const hint = document.querySelector('.drag-hint');
    if (hint) {
        hint.remove();
    }
}

// 初始化模态框
function initializeModals() {
    const modals = document.querySelectorAll('.modal');
    const closeButtons = document.querySelectorAll('.close');
    
    closeButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            modal.style.display = 'none';
        });
    });
    
    // 点击模态框外部关闭
    window.addEventListener('click', function(e) {
        modals.forEach(modal => {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });
    });
}

// 显示预览
function showPreview() {
    const modal = document.getElementById('previewModal');
    const previewContent = document.getElementById('preview-content');
    
    // 生成预览HTML
    const previewHTML = generatePreviewHTML();
    previewContent.innerHTML = previewHTML;
    
    modal.style.display = 'block';
}

// 显示导出
function showExport() {
    const modal = document.getElementById('exportModal');
    const mediawikiTextarea = document.getElementById('export-mediawiki');
    const cssTextarea = document.getElementById('export-css');
    
    // 生成代码
    const mediawikiCode = generateMediaWikiCode();
    const cssCode = generateCSSCode();
    
    mediawikiTextarea.value = mediawikiCode;
    cssTextarea.value = cssCode;
    
    modal.style.display = 'block';
}

// 清空画布
function clearCanvas() {
    if (confirm('确定要清空所有内容吗？此操作不可撤销。')) {
        designData.elements = [];
        selectedElement = null;
        
        // 清空画布
        const elements = designCanvas.querySelectorAll('.design-element');
        elements.forEach(el => el.remove());
        
        // 显示占位符
        const placeholder = designCanvas.querySelector('.canvas-placeholder');
        if (placeholder) {
            placeholder.style.display = 'block';
        }
        
        updatePropertiesPanel();
        updateCode();
    }
}

// 复制代码到剪贴板
function copyCode(textareaId) {
    const textarea = document.getElementById(textareaId);
    textarea.select();
    document.execCommand('copy');
    
    // 显示复制成功提示
    const btn = textarea.nextElementSibling;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-check"></i> 已复制';
    btn.style.backgroundColor = '#10b981';
    
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.style.backgroundColor = '';
    }, 2000);
}

// 更新代码显示
function updateCode() {
    const mediawikiCode = generateMediaWikiCode();
    const cssCode = generateCSSCode();
    
    const mediawikiTextarea = document.getElementById('mediawiki-code');
    const cssTextarea = document.getElementById('css-code');
    
    if (mediawikiTextarea) mediawikiTextarea.value = mediawikiCode;
    if (cssTextarea) cssTextarea.value = cssCode;
}
